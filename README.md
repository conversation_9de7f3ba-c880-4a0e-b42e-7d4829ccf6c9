# 💠 Prompt Studio

A comprehensive AI prompt engineering and testing platform built with React, TypeScript, and modern web technologies. Test prompts across multiple AI models, create intelligent agents, build complex workflows, and analyze results with advanced scoring systems.

## ✨ Features

### 🧪 Prompt Testing
- **Multi-Model Testing**: Test prompts across OpenAI GPT-4, Anthropic Claude, and more
- **Side-by-Side Comparison**: Compare responses from different models
- **Variable Substitution**: Use dynamic variables in your prompts
- **Automatic Scoring**: AI-powered scoring for fidelity, adherence, consistency, and creativity

### 🔀 Prompt Variations
- **A/B Testing**: Create and test multiple variations of your prompts
- **Statistical Analysis**: Compare performance across variations
- **Auto-Generation**: AI-assisted variation generation
- **Performance Tracking**: Monitor improvement over time

### 👥 Agent Studio
- **Pre-built Agents**: Code Reviewer, Product Manager, UX Designer, and more
- **Custom Agents**: Create agents with specific personalities and expertise
- **Multi-Agent Conversations**: Simulate complex interactions
- **Agent Simulation**: Test how agents respond to different scenarios

### 🏗️ Project Simulator
- **Complex Scenarios**: Multi-step simulations with multiple agents
- **Real-world Testing**: Simulate actual project workflows
- **Collaboration Analysis**: See how agents work together
- **Comprehensive Results**: Detailed analysis of simulation outcomes

### 🔗 Chain Linker Canvas
- **Visual Workflow Builder**: Drag-and-drop interface for prompt chains
- **Node-Based Design**: Connect prompts, agents, and conditions
- **Conditional Logic**: Build complex decision trees
- **Execution Engine**: Run workflows and see results

### 📊 Results Dashboard
- **Comprehensive Analytics**: View all test results in one place
- **Performance Metrics**: Track scoring trends over time
- **Export Capabilities**: Export results in multiple formats
- **Live Forking**: Fork successful prompts directly from results

### 🔧 Advanced Features
- **Persistent Storage**: IndexedDB-based local storage with backup/restore
- **Export Tools**: Support for .promptx, JSON, and direct integrations
- **Error Handling**: Comprehensive error management and recovery
- **Performance Monitoring**: Built-in performance tracking and optimization
- **User Onboarding**: Interactive tour for new users

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- AI API keys (OpenAI, Anthropic, etc.)

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   Create a `.env` file and add your API keys:
   ```env
   VITE_OPENAI_API_KEY=your_openai_api_key
   VITE_ANTHROPIC_API_KEY=your_anthropic_api_key
   ```

3. **Start the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### First Steps
1. Take the interactive onboarding tour
2. Configure your AI API keys in Settings
3. Try the Prompt Tester with a simple prompt
4. Explore the Agent Studio and create your first agent
5. Build a workflow in the Chain Linker Canvas

- Navigate to the main page of your repository.
- Click on the "Code" button (green button) near the top right.
- Select the "Codespaces" tab.
- Click on "New codespace" to launch a new Codespace environment.
- Edit files directly within the Codespace and commit and push your changes once you're done.

## What technologies are used for this project?

This project is built with:

- Vite
- TypeScript
- React
- shadcn-ui
- Tailwind CSS

## How can I deploy this project?

Simply open [Lovable](https://lovable.dev/projects/d94ed4cd-e840-4a9f-9e1f-867866d90cae) and click on Share -> Publish.

## Can I connect a custom domain to my Lovable project?

Yes, you can!

To connect a domain, navigate to Project > Settings > Domains and click Connect Domain.

Read more here: [Setting up a custom domain](https://docs.lovable.dev/tips-tricks/custom-domain#step-by-step-guide)
