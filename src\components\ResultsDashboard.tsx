
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Clock, Zap, Download, Eye, GitBranch } from 'lucide-react';
import { useState } from 'react';
import { ForkDialog } from './ForkDialog';
import { ExportDialog } from './ExportDialog';
import { liveForkingService, ForkableResult } from '@/lib/live-forking';

interface ResultsDashboardProps {
  results: any[];
}

export const ResultsDashboard = ({ results }: ResultsDashboardProps) => {
  const [selectedResult, setSelectedResult] = useState(null);
  const [forkDialogOpen, setForkDialogOpen] = useState(false);
  const [forkableContent, setForkableContent] = useState<ForkableResult | null>(null);
  const [exportDialogOpen, setExportDialogOpen] = useState(false);

  if (results.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="text-slate-400 mb-4">
          <Eye className="w-16 h-16 mx-auto mb-4 opacity-50" />
          <h3 className="text-xl font-semibold">No Results Yet</h3>
          <p>Run your first prompt test to see results here.</p>
        </div>
      </div>
    );
  }

  const getResultDescription = (result: any) => {
    if (result.type === 'agent_simulation') {
      return `Scenario: "${result.scenario?.slice(0, 100) || 'No scenario'}..."`;
    } else if (result.type === 'variation_testing') {
      return `Variation Testing: ${result.variations?.length || 0} variations tested`;
    } else {
      return `Prompt: "${result.prompt?.slice(0, 100) || 'No prompt'}..."`;
    }
  };

  const getResultTitle = (result: any) => {
    if (result.type === 'agent_simulation') {
      return 'Agent Simulation';
    } else if (result.type === 'variation_testing') {
      return 'Variation Testing';
    } else {
      return 'Prompt Test';
    }
  };

  const handleFork = (result: any) => {
    const forkable = liveForkingService.extractForkableContent(result);
    if (forkable) {
      setForkableContent(forkable);
      setForkDialogOpen(true);
    }
  };

  const canFork = (result: any) => {
    return ['prompt_test', 'agent_simulation', 'variation_testing', 'project_simulation'].includes(result.type);
  };

  const renderResultContent = (result: any) => {
    if (result.type === 'agent_simulation') {
      return (
        <div className="grid gap-4">
          {result.agents?.map((agent: any, index: number) => (
            <Card key={index} className="bg-slate-900/50 border-slate-600 p-4">
              <div className="mb-3">
                <h4 className="font-semibold text-slate-200">{agent.name}</h4>
                <p className="text-xs text-slate-400">{agent.role}</p>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <span className="text-slate-300">Sentiment: {agent.sentiment}</span>
                </div>

                <div className="flex items-center gap-2 text-sm">
                  <span className="text-slate-300">Confidence: {Math.round(agent.confidence)}%</span>
                </div>

                <div className="mt-3 p-2 bg-slate-800 rounded text-xs text-slate-300">
                  {agent.response?.slice(0, 100) || 'No response'}...
                </div>
              </div>
            </Card>
          )) || <div className="text-slate-400">No agents found</div>}
        </div>
      );
    }

    if (result.type === 'variation_testing') {
      return (
        <div className="grid gap-4">
          {result.variations?.map((variation: any, index: number) => (
            <Card key={index} className="bg-slate-900/50 border-slate-600 p-4">
              <div className="mb-3">
                <h4 className="font-semibold text-slate-200">{variation.name}</h4>
                <p className="text-xs text-slate-400">Variation {index + 1}</p>
              </div>

              <div className="space-y-3">
                {variation.results?.map((varResult: any, varIndex: number) => (
                  <div key={varIndex} className="border-t border-slate-700 pt-3">
                    <div className="flex items-center gap-2 text-sm mb-2">
                      <span className="text-slate-300">{varResult.model}</span>
                    </div>

                    <div className="space-y-2">
                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span className="text-slate-400">Fidelity</span>
                          <span className="text-slate-300">{Math.round(varResult.score?.fidelity || 0)}%</span>
                        </div>
                        <Progress value={varResult.score?.fidelity || 0} className="h-1" />
                      </div>

                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span className="text-slate-400">Adherence</span>
                          <span className="text-slate-300">{Math.round(varResult.score?.adherence || 0)}%</span>
                        </div>
                        <Progress value={varResult.score?.adherence || 0} className="h-1" />
                      </div>

                      <div>
                        <div className="flex justify-between text-xs mb-1">
                          <span className="text-slate-400">Consistency</span>
                          <span className="text-slate-300">{Math.round(varResult.score?.consistency || 0)}%</span>
                        </div>
                        <Progress value={varResult.score?.consistency || 0} className="h-1" />
                      </div>
                    </div>

                    <div className="mt-3 p-2 bg-slate-800 rounded text-xs text-slate-300">
                      {varResult.response?.slice(0, 100) || 'No response'}...
                    </div>
                  </div>
                ))}
              </div>
            </Card>
          )) || <div className="text-slate-400">No variations found</div>}
        </div>
      );
    }

    // Default prompt testing results
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {result.results?.map((modelResult: any, index: number) => (
          <Card key={index} className="bg-slate-900/50 border-slate-600 p-4">
            <div className="mb-3">
              <h4 className="font-semibold text-slate-200">{modelResult.model}</h4>
              <p className="text-xs text-slate-400">{modelResult.provider}</p>
            </div>

            <div className="space-y-3">
              <div className="flex items-center gap-2 text-sm">
                <Clock className="w-3 h-3 text-slate-400" />
                <span className="text-slate-300">
                  {Math.round(modelResult.timeToCompletion)}ms
                </span>
              </div>

              <div className="flex items-center gap-2 text-sm">
                <Zap className="w-3 h-3 text-slate-400" />
                <span className="text-slate-300">
                  {modelResult.tokens} tokens
                </span>
              </div>

              <div className="space-y-2">
                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-slate-400">Fidelity</span>
                    <span className="text-slate-300">{Math.round(modelResult.score?.fidelity || 0)}%</span>
                  </div>
                  <Progress value={modelResult.score?.fidelity || 0} className="h-1" />
                </div>

                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-slate-400">Adherence</span>
                    <span className="text-slate-300">{Math.round(modelResult.score?.adherence || 0)}%</span>
                  </div>
                  <Progress value={modelResult.score?.adherence || 0} className="h-1" />
                </div>

                <div>
                  <div className="flex justify-between text-xs mb-1">
                    <span className="text-slate-400">Consistency</span>
                    <span className="text-slate-300">{Math.round(modelResult.score?.consistency || 0)}%</span>
                  </div>
                  <Progress value={modelResult.score?.consistency || 0} className="h-1" />
                </div>
              </div>

              <div className="mt-3 p-2 bg-slate-800 rounded text-xs text-slate-300">
                {modelResult.response?.slice(0, 100) || 'No response'}...
              </div>
            </div>
          </Card>
        )) || <div className="text-slate-400">No results found</div>}
      </div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200">Test Results</h2>
        <Button
          onClick={() => setExportDialogOpen(true)}
          variant="outline"
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          <Download className="w-4 h-4 mr-2" />
          Export All
        </Button>
      </div>

      <div className="grid gap-6">
        {results.map((result) => (
          <Card key={result.id} className="bg-slate-800/50 border-slate-700 p-6">
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-slate-200">
                  {getResultTitle(result)} #{result.id}
                </h3>
                <div className="flex items-center gap-2">
                  {canFork(result) && (
                    <Button
                      onClick={() => handleFork(result)}
                      size="sm"
                      variant="outline"
                      className="border-purple-600/50 text-purple-300 hover:bg-purple-600/20"
                    >
                      <GitBranch className="w-3 h-3 mr-1" />
                      Fork
                    </Button>
                  )}
                  <Badge variant="secondary" className="bg-slate-700 text-slate-300">
                    {new Date(result.timestamp).toLocaleString()}
                  </Badge>
                </div>
              </div>
              <p className="text-slate-400 text-sm">
                {getResultDescription(result)}
              </p>
            </div>

            {renderResultContent(result)}
          </Card>
        ))}
      </div>

      <ForkDialog
        open={forkDialogOpen}
        onOpenChange={setForkDialogOpen}
        forkableContent={forkableContent}
      />

      <ExportDialog
        open={exportDialogOpen}
        onOpenChange={setExportDialogOpen}
      />
    </div>
  );
};
