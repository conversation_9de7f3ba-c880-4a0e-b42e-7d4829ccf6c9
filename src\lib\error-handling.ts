export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  API_ERROR = 'API_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  RATE_LIMIT_ERROR = 'RATE_LIMIT_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

export interface ErrorContext {
  component?: string;
  action?: string;
  userId?: string;
  timestamp: Date;
  metadata?: Record<string, any>;
}

export class AppError extends Error {
  public readonly type: ErrorType;
  public readonly context: ErrorContext;
  public readonly isRetryable: boolean;
  public readonly userMessage: string;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    context: Partial<ErrorContext> = {},
    isRetryable: boolean = false,
    userMessage?: string
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.context = {
      timestamp: new Date(),
      ...context
    };
    this.isRetryable = isRetryable;
    this.userMessage = userMessage || this.getDefaultUserMessage();
  }

  private getDefaultUserMessage(): string {
    switch (this.type) {
      case ErrorType.NETWORK_ERROR:
        return 'Network connection failed. Please check your internet connection and try again.';
      case ErrorType.API_ERROR:
        return 'Service temporarily unavailable. Please try again in a few moments.';
      case ErrorType.VALIDATION_ERROR:
        return 'Please check your input and try again.';
      case ErrorType.DATABASE_ERROR:
        return 'Data storage error. Your changes may not have been saved.';
      case ErrorType.AUTHENTICATION_ERROR:
        return 'Authentication failed. Please check your API keys in settings.';
      case ErrorType.RATE_LIMIT_ERROR:
        return 'Rate limit exceeded. Please wait a moment before trying again.';
      default:
        return 'An unexpected error occurred. Please try again.';
    }
  }

  toJSON() {
    return {
      name: this.name,
      message: this.message,
      type: this.type,
      context: this.context,
      isRetryable: this.isRetryable,
      userMessage: this.userMessage,
      stack: this.stack
    };
  }
}

export class NetworkError extends AppError {
  constructor(message: string, context: Partial<ErrorContext> = {}) {
    super(message, ErrorType.NETWORK_ERROR, context, true);
  }
}

export class APIError extends AppError {
  public readonly statusCode?: number;
  public readonly provider?: string;

  constructor(
    message: string,
    statusCode?: number,
    provider?: string,
    context: Partial<ErrorContext> = {}
  ) {
    const isRetryable = statusCode ? statusCode >= 500 || statusCode === 429 : false;
    super(message, ErrorType.API_ERROR, context, isRetryable);
    this.statusCode = statusCode;
    this.provider = provider;
  }
}

export class ValidationError extends AppError {
  public readonly field?: string;
  public readonly validationRules?: string[];

  constructor(
    message: string,
    field?: string,
    validationRules?: string[],
    context: Partial<ErrorContext> = {}
  ) {
    super(message, ErrorType.VALIDATION_ERROR, context, false);
    this.field = field;
    this.validationRules = validationRules;
  }
}

export class DatabaseError extends AppError {
  public readonly operation?: string;
  public readonly storeName?: string;

  constructor(
    message: string,
    operation?: string,
    storeName?: string,
    context: Partial<ErrorContext> = {}
  ) {
    super(message, ErrorType.DATABASE_ERROR, context, true);
    this.operation = operation;
    this.storeName = storeName;
  }
}

export class AuthenticationError extends AppError {
  public readonly provider?: string;

  constructor(
    message: string,
    provider?: string,
    context: Partial<ErrorContext> = {}
  ) {
    super(message, ErrorType.AUTHENTICATION_ERROR, context, false);
    this.provider = provider;
  }
}

export class RateLimitError extends AppError {
  public readonly retryAfter?: number;
  public readonly provider?: string;

  constructor(
    message: string,
    retryAfter?: number,
    provider?: string,
    context: Partial<ErrorContext> = {}
  ) {
    super(message, ErrorType.RATE_LIMIT_ERROR, context, true);
    this.retryAfter = retryAfter;
    this.provider = provider;
  }
}

class ErrorHandler {
  private errorLog: AppError[] = [];
  private maxLogSize = 100;

  /**
   * Handle and log an error
   */
  handle(error: Error | AppError, context: Partial<ErrorContext> = {}): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
      // Merge additional context
      appError.context = { ...appError.context, ...context };
    } else {
      appError = this.convertToAppError(error, context);
    }

    this.logError(appError);
    this.reportError(appError);

    return appError;
  }

  /**
   * Convert a generic error to AppError
   */
  private convertToAppError(error: Error, context: Partial<ErrorContext>): AppError {
    // Network errors
    if (error.message.includes('fetch') || error.message.includes('network')) {
      return new NetworkError(error.message, context);
    }

    // API errors (check for status codes in message)
    const statusMatch = error.message.match(/(\d{3})/);
    if (statusMatch) {
      const statusCode = parseInt(statusMatch[1]);
      return new APIError(error.message, statusCode, undefined, context);
    }

    // Database errors
    if (error.message.includes('database') || error.message.includes('IndexedDB')) {
      return new DatabaseError(error.message, undefined, undefined, context);
    }

    // Authentication errors
    if (error.message.includes('unauthorized') || error.message.includes('API key')) {
      return new AuthenticationError(error.message, undefined, context);
    }

    // Rate limit errors
    if (error.message.includes('rate limit') || error.message.includes('429')) {
      return new RateLimitError(error.message, undefined, undefined, context);
    }

    // Default to unknown error
    return new AppError(error.message, ErrorType.UNKNOWN_ERROR, context);
  }

  /**
   * Log error to internal log
   */
  private logError(error: AppError): void {
    this.errorLog.unshift(error);
    
    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Console logging for development
    if (process.env.NODE_ENV === 'development') {
      console.group(`🚨 ${error.type}: ${error.message}`);
      console.error('Error:', error);
      console.log('Context:', error.context);
      console.log('User Message:', error.userMessage);
      console.log('Retryable:', error.isRetryable);
      if (error.stack) {
        console.log('Stack:', error.stack);
      }
      console.groupEnd();
    }
  }

  /**
   * Report error to external service (placeholder)
   */
  private reportError(error: AppError): void {
    // In a real application, this would send to error reporting service
    // like Sentry, LogRocket, etc.
    
    if (process.env.NODE_ENV === 'production') {
      // Example: Send to error reporting service
      // errorReportingService.captureException(error);
    }
  }

  /**
   * Get recent errors
   */
  getRecentErrors(limit: number = 10): AppError[] {
    return this.errorLog.slice(0, limit);
  }

  /**
   * Clear error log
   */
  clearLog(): void {
    this.errorLog = [];
  }

  /**
   * Get error statistics
   */
  getErrorStats(): {
    total: number;
    byType: Record<ErrorType, number>;
    recentCount: number;
  } {
    const byType = Object.values(ErrorType).reduce((acc, type) => {
      acc[type] = 0;
      return acc;
    }, {} as Record<ErrorType, number>);

    this.errorLog.forEach(error => {
      byType[error.type]++;
    });

    const oneHourAgo = new Date(Date.now() - 60 * 60 * 1000);
    const recentCount = this.errorLog.filter(error => 
      error.context.timestamp > oneHourAgo
    ).length;

    return {
      total: this.errorLog.length,
      byType,
      recentCount
    };
  }
}

export const errorHandler = new ErrorHandler();

/**
 * Retry wrapper for operations that might fail
 */
export async function withRetry<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000,
  context: Partial<ErrorContext> = {}
): Promise<T> {
  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error as Error;
      
      const appError = errorHandler.handle(lastError, {
        ...context,
        metadata: { attempt, maxRetries }
      });

      // Don't retry if error is not retryable
      if (!appError.isRetryable || attempt === maxRetries) {
        throw appError;
      }

      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }

  throw errorHandler.handle(lastError!, context);
}

/**
 * Safe async wrapper that catches and handles errors
 */
export async function safeAsync<T>(
  operation: () => Promise<T>,
  context: Partial<ErrorContext> = {},
  fallback?: T
): Promise<T | undefined> {
  try {
    return await operation();
  } catch (error) {
    const appError = errorHandler.handle(error as Error, context);
    
    if (fallback !== undefined) {
      return fallback;
    }
    
    // Re-throw if no fallback provided
    throw appError;
  }
}

/**
 * Error boundary hook for React components
 */
export function useErrorHandler() {
  return {
    handleError: (error: Error, context?: Partial<ErrorContext>) => {
      return errorHandler.handle(error, context);
    },
    getRecentErrors: errorHandler.getRecentErrors.bind(errorHandler),
    clearLog: errorHandler.clearLog.bind(errorHandler),
    getErrorStats: errorHandler.getErrorStats.bind(errorHandler)
  };
}
