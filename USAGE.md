# 🚀 How to Run Prompt Studio

This guide provides multiple ways to start and run the Prompt Studio application.

## 🎯 Quick Start (Recommended)

### Windows Users
1. **Double-click** `quick-start.cmd` in the project folder
2. The script will automatically:
   - Check for Node.js installation
   - Install dependencies if needed
   - Create environment file if missing
   - Start the development server
3. Your browser will open to `http://localhost:5173`

### All Platforms
1. **Double-click** `start-prompt-studio.cmd` (Windows) or run `./start-prompt-studio.sh` (Mac/Linux)
2. Choose from the interactive menu:
   - Start development server
   - Build for production
   - Run tests
   - And more options

## 📋 Prerequisites

Before running Prompt Studio, ensure you have:

- **Node.js 18+** installed ([Download here](https://nodejs.org/))
- **npm** (comes with Node.js)
- **AI API Keys** (at least one):
  - OpenAI API key from [platform.openai.com](https://platform.openai.com/api-keys)
  - Anthropic API key from [console.anthropic.com](https://console.anthropic.com/)

## 🛠️ Manual Setup

If you prefer manual setup:

### 1. Install Dependencies
```bash
npm install
```

### 2. Configure Environment
```bash
# Copy the example environment file
cp .env.example .env

# Edit .env and add your API keys
# VITE_OPENAI_API_KEY=your_actual_openai_key
# VITE_ANTHROPIC_API_KEY=your_actual_anthropic_key
```

### 3. Start Development Server
```bash
npm run dev
# or
npm start
```

### 4. Open Browser
Navigate to `http://localhost:5173`

## 🎮 Available Commands

| Command | Description |
|---------|-------------|
| `npm run dev` | Start development server |
| `npm start` | Alias for dev server |
| `npm run build` | Build for production |
| `npm run preview` | Preview production build |
| `npm test` | Run tests |
| `npm run test:watch` | Run tests in watch mode |
| `npm run test:coverage` | Run tests with coverage |
| `npm run lint` | Run ESLint |
| `npm run setup` | Install deps and start dev server |

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the project root with:

```env
# Required: OpenAI API Key
VITE_OPENAI_API_KEY=sk-your-openai-key-here

# Required: Anthropic API Key  
VITE_ANTHROPIC_API_KEY=sk-ant-your-anthropic-key-here

# Optional: Additional providers
VITE_GEMINI_API_KEY=your-gemini-key-here
VITE_MISTRAL_API_KEY=your-mistral-key-here

# Optional: Development settings
VITE_DEV_MODE=true
VITE_DEBUG_LOGGING=false
```

### Getting API Keys

#### OpenAI
1. Go to [platform.openai.com](https://platform.openai.com/)
2. Sign up or log in
3. Navigate to API Keys section
4. Create a new secret key
5. Copy the key (starts with `sk-`)

#### Anthropic
1. Go to [console.anthropic.com](https://console.anthropic.com/)
2. Sign up or log in
3. Navigate to API Keys
4. Create a new key
5. Copy the key (starts with `sk-ant-`)

## 🌐 Deployment

### Build for Production
```bash
npm run build
```

The built files will be in the `dist/` directory.

### Deploy to Vercel
```bash
npm install -g vercel
vercel
```

### Deploy to Netlify
1. Run `npm run build`
2. Upload the `dist/` folder to Netlify
3. Set environment variables in Netlify dashboard

### Deploy to GitHub Pages
1. Build the project: `npm run build`
2. Push the `dist/` folder to `gh-pages` branch
3. Enable GitHub Pages in repository settings

## 🧪 Testing

### Run All Tests
```bash
npm test
```

### Run Tests in Watch Mode
```bash
npm run test:watch
```

### Run Tests with Coverage
```bash
npm run test:coverage
```

## 🐛 Troubleshooting

### Common Issues

#### "Node.js not found"
- Install Node.js from [nodejs.org](https://nodejs.org/)
- Restart your terminal/command prompt
- Verify installation: `node --version`

#### "npm command not found"
- npm comes with Node.js
- If missing, reinstall Node.js
- Or install npm separately: `npm install -g npm`

#### "Dependencies installation failed"
- Check internet connection
- Clear npm cache: `npm cache clean --force`
- Delete `node_modules` and `package-lock.json`, then run `npm install`

#### "API key errors"
- Verify your API keys are correct
- Check that keys are properly set in `.env` file
- Ensure no extra spaces or quotes around keys
- Restart the development server after changing `.env`

#### "Port 5173 already in use"
- Stop other Vite/development servers
- Or use a different port: `npm run dev -- --port 3000`

#### "Build fails"
- Check for TypeScript errors: `npm run lint`
- Ensure all dependencies are installed
- Clear build cache and try again

### Getting Help

If you encounter issues:

1. Check the [README.md](README.md) for detailed documentation
2. Review the browser console for error messages
3. Check the terminal output for build/runtime errors
4. Ensure all prerequisites are met
5. Try the troubleshooting steps above

## 🎉 First Time Setup

1. **Run the launcher**: Double-click `start-prompt-studio.cmd` or `quick-start.cmd`
2. **Add API keys**: Edit the `.env` file when prompted
3. **Take the tour**: Use the interactive onboarding when the app starts
4. **Test a prompt**: Try the Prompt Tester with a simple prompt
5. **Explore features**: Visit Agent Studio, Chain Linker, and other sections

## 📱 Browser Compatibility

Prompt Studio works best with modern browsers:
- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## 🔄 Updates

To update Prompt Studio:
```bash
git pull origin main
npm install
npm run dev
```

---

**Happy prompting! 🚀**
