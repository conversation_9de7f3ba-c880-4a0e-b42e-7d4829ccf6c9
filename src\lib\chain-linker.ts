export interface ChainNode {
  id: string;
  type: 'prompt' | 'agent' | 'condition' | 'output' | 'input';
  position: { x: number; y: number };
  data: {
    title: string;
    description?: string;
    content?: string;
    config?: Record<string, any>;
  };
  inputs: Array<{
    id: string;
    label: string;
    type: string;
    required: boolean;
  }>;
  outputs: Array<{
    id: string;
    label: string;
    type: string;
  }>;
}

export interface ChainConnection {
  id: string;
  sourceNodeId: string;
  sourceOutputId: string;
  targetNodeId: string;
  targetInputId: string;
  data?: {
    transform?: string;
    condition?: string;
  };
}

export interface ChainFlow {
  id: string;
  name: string;
  description: string;
  nodes: ChainNode[];
  connections: ChainConnection[];
  metadata: {
    createdAt: Date;
    updatedAt: Date;
    version: string;
    tags: string[];
  };
}

export interface ExecutionContext {
  variables: Record<string, any>;
  results: Record<string, any>;
  currentNode?: string;
  executionPath: string[];
}

export interface ExecutionResult {
  success: boolean;
  results: Record<string, any>;
  executionPath: string[];
  duration: number;
  errors?: Array<{
    nodeId: string;
    message: string;
  }>;
}

class ChainLinkerService {
  /**
   * Create a new chain flow
   */
  createFlow(name: string, description: string): ChainFlow {
    return {
      id: `flow_${Date.now()}`,
      name,
      description,
      nodes: [],
      connections: [],
      metadata: {
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0',
        tags: []
      }
    };
  }

  /**
   * Add a node to the flow
   */
  addNode(flow: ChainFlow, nodeType: ChainNode['type'], position: { x: number; y: number }): ChainNode {
    const nodeTemplates = this.getNodeTemplates();
    const template = nodeTemplates[nodeType];
    
    const node: ChainNode = {
      id: `node_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type: nodeType,
      position,
      data: {
        title: template.title,
        description: template.description,
        content: template.defaultContent || '',
        config: { ...template.defaultConfig }
      },
      inputs: [...template.inputs],
      outputs: [...template.outputs]
    };

    flow.nodes.push(node);
    flow.metadata.updatedAt = new Date();
    
    return node;
  }

  /**
   * Remove a node from the flow
   */
  removeNode(flow: ChainFlow, nodeId: string): void {
    // Remove the node
    flow.nodes = flow.nodes.filter(node => node.id !== nodeId);
    
    // Remove all connections involving this node
    flow.connections = flow.connections.filter(
      conn => conn.sourceNodeId !== nodeId && conn.targetNodeId !== nodeId
    );
    
    flow.metadata.updatedAt = new Date();
  }

  /**
   * Add a connection between nodes
   */
  addConnection(
    flow: ChainFlow,
    sourceNodeId: string,
    sourceOutputId: string,
    targetNodeId: string,
    targetInputId: string
  ): ChainConnection {
    // Validate connection
    const sourceNode = flow.nodes.find(n => n.id === sourceNodeId);
    const targetNode = flow.nodes.find(n => n.id === targetNodeId);
    
    if (!sourceNode || !targetNode) {
      throw new Error('Source or target node not found');
    }

    const sourceOutput = sourceNode.outputs.find(o => o.id === sourceOutputId);
    const targetInput = targetNode.inputs.find(i => i.id === targetInputId);
    
    if (!sourceOutput || !targetInput) {
      throw new Error('Source output or target input not found');
    }

    // Check for cycles
    if (this.wouldCreateCycle(flow, sourceNodeId, targetNodeId)) {
      throw new Error('Connection would create a cycle');
    }

    const connection: ChainConnection = {
      id: `conn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      sourceNodeId,
      sourceOutputId,
      targetNodeId,
      targetInputId
    };

    flow.connections.push(connection);
    flow.metadata.updatedAt = new Date();
    
    return connection;
  }

  /**
   * Remove a connection
   */
  removeConnection(flow: ChainFlow, connectionId: string): void {
    flow.connections = flow.connections.filter(conn => conn.id !== connectionId);
    flow.metadata.updatedAt = new Date();
  }

  /**
   * Execute a chain flow
   */
  async executeFlow(flow: ChainFlow, initialVariables: Record<string, any> = {}): Promise<ExecutionResult> {
    const startTime = Date.now();
    const context: ExecutionContext = {
      variables: { ...initialVariables },
      results: {},
      executionPath: []
    };

    const errors: Array<{ nodeId: string; message: string }> = [];

    try {
      // Find entry points (nodes with no incoming connections)
      const entryNodes = this.findEntryNodes(flow);
      
      if (entryNodes.length === 0) {
        throw new Error('No entry points found in the flow');
      }

      // Execute nodes in topological order
      const executionOrder = this.getExecutionOrder(flow);
      
      for (const nodeId of executionOrder) {
        try {
          await this.executeNode(flow, nodeId, context);
          context.executionPath.push(nodeId);
        } catch (error) {
          errors.push({
            nodeId,
            message: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return {
        success: errors.length === 0,
        results: context.results,
        executionPath: context.executionPath,
        duration: Date.now() - startTime,
        errors: errors.length > 0 ? errors : undefined
      };
    } catch (error) {
      return {
        success: false,
        results: context.results,
        executionPath: context.executionPath,
        duration: Date.now() - startTime,
        errors: [{
          nodeId: 'flow',
          message: error instanceof Error ? error.message : 'Flow execution failed'
        }]
      };
    }
  }

  /**
   * Get node templates for different types
   */
  private getNodeTemplates(): Record<string, any> {
    return {
      input: {
        title: 'Input',
        description: 'Provides input data to the flow',
        inputs: [],
        outputs: [
          { id: 'value', label: 'Value', type: 'any' }
        ],
        defaultConfig: {
          inputType: 'text',
          defaultValue: ''
        }
      },
      prompt: {
        title: 'Prompt Node',
        description: 'Executes a prompt with an AI model',
        inputs: [
          { id: 'input', label: 'Input', type: 'string', required: true },
          { id: 'variables', label: 'Variables', type: 'object', required: false }
        ],
        outputs: [
          { id: 'response', label: 'Response', type: 'string' },
          { id: 'metadata', label: 'Metadata', type: 'object' }
        ],
        defaultContent: 'Enter your prompt here...',
        defaultConfig: {
          model: 'gpt-4',
          temperature: 0.7,
          maxTokens: 1024
        }
      },
      agent: {
        title: 'Agent Node',
        description: 'Processes input through an AI agent',
        inputs: [
          { id: 'scenario', label: 'Scenario', type: 'string', required: true },
          { id: 'context', label: 'Context', type: 'object', required: false }
        ],
        outputs: [
          { id: 'response', label: 'Response', type: 'string' },
          { id: 'confidence', label: 'Confidence', type: 'number' },
          { id: 'insights', label: 'Insights', type: 'array' }
        ],
        defaultConfig: {
          agentId: '',
          includeReasoning: true
        }
      },
      condition: {
        title: 'Condition',
        description: 'Routes flow based on conditions',
        inputs: [
          { id: 'input', label: 'Input', type: 'any', required: true }
        ],
        outputs: [
          { id: 'true', label: 'True', type: 'any' },
          { id: 'false', label: 'False', type: 'any' }
        ],
        defaultContent: 'input.length > 0',
        defaultConfig: {
          conditionType: 'javascript'
        }
      },
      output: {
        title: 'Output',
        description: 'Collects and formats final output',
        inputs: [
          { id: 'data', label: 'Data', type: 'any', required: true }
        ],
        outputs: [],
        defaultConfig: {
          format: 'json',
          includeMetadata: true
        }
      }
    };
  }

  /**
   * Check if adding a connection would create a cycle
   */
  private wouldCreateCycle(flow: ChainFlow, sourceNodeId: string, targetNodeId: string): boolean {
    // Simple cycle detection using DFS
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      // Check all outgoing connections
      const outgoingConnections = flow.connections.filter(conn => conn.sourceNodeId === nodeId);
      for (const conn of outgoingConnections) {
        if (hasCycle(conn.targetNodeId)) return true;
      }

      // Add the hypothetical new connection
      if (nodeId === sourceNodeId && hasCycle(targetNodeId)) return true;

      recursionStack.delete(nodeId);
      return false;
    };

    return hasCycle(sourceNodeId);
  }

  /**
   * Find nodes with no incoming connections
   */
  private findEntryNodes(flow: ChainFlow): string[] {
    const nodesWithIncoming = new Set(flow.connections.map(conn => conn.targetNodeId));
    return flow.nodes
      .filter(node => !nodesWithIncoming.has(node.id))
      .map(node => node.id);
  }

  /**
   * Get execution order using topological sort
   */
  private getExecutionOrder(flow: ChainFlow): string[] {
    const inDegree = new Map<string, number>();
    const adjList = new Map<string, string[]>();

    // Initialize
    flow.nodes.forEach(node => {
      inDegree.set(node.id, 0);
      adjList.set(node.id, []);
    });

    // Build adjacency list and calculate in-degrees
    flow.connections.forEach(conn => {
      adjList.get(conn.sourceNodeId)?.push(conn.targetNodeId);
      inDegree.set(conn.targetNodeId, (inDegree.get(conn.targetNodeId) || 0) + 1);
    });

    // Topological sort
    const queue: string[] = [];
    const result: string[] = [];

    // Add nodes with no incoming edges
    inDegree.forEach((degree, nodeId) => {
      if (degree === 0) queue.push(nodeId);
    });

    while (queue.length > 0) {
      const nodeId = queue.shift()!;
      result.push(nodeId);

      // Process neighbors
      adjList.get(nodeId)?.forEach(neighborId => {
        const newDegree = (inDegree.get(neighborId) || 0) - 1;
        inDegree.set(neighborId, newDegree);
        
        if (newDegree === 0) {
          queue.push(neighborId);
        }
      });
    }

    return result;
  }

  /**
   * Execute a single node
   */
  private async executeNode(flow: ChainFlow, nodeId: string, context: ExecutionContext): Promise<void> {
    const node = flow.nodes.find(n => n.id === nodeId);
    if (!node) throw new Error(`Node ${nodeId} not found`);

    context.currentNode = nodeId;

    // Collect inputs from connected nodes
    const inputs: Record<string, any> = {};
    const incomingConnections = flow.connections.filter(conn => conn.targetNodeId === nodeId);
    
    for (const conn of incomingConnections) {
      const sourceResult = context.results[conn.sourceNodeId];
      if (sourceResult && sourceResult[conn.sourceOutputId] !== undefined) {
        inputs[conn.targetInputId] = sourceResult[conn.sourceOutputId];
      }
    }

    // Execute node based on type
    let result: any = {};

    switch (node.type) {
      case 'input':
        result = { value: node.data.config?.defaultValue || '' };
        break;
        
      case 'prompt':
        result = await this.executePromptNode(node, inputs);
        break;
        
      case 'agent':
        result = await this.executeAgentNode(node, inputs);
        break;
        
      case 'condition':
        result = this.executeConditionNode(node, inputs);
        break;
        
      case 'output':
        result = this.executeOutputNode(node, inputs);
        break;
        
      default:
        throw new Error(`Unknown node type: ${node.type}`);
    }

    context.results[nodeId] = result;
  }

  private async executePromptNode(node: ChainNode, inputs: Record<string, any>): Promise<any> {
    // This would integrate with the AI service
    // For now, return mock data
    return {
      response: `Mock response for prompt: ${node.data.content}`,
      metadata: {
        model: node.data.config?.model || 'gpt-4',
        tokens: 150,
        duration: 1200
      }
    };
  }

  private async executeAgentNode(node: ChainNode, inputs: Record<string, any>): Promise<any> {
    // This would integrate with the agent simulation service
    return {
      response: `Mock agent response for scenario: ${inputs.scenario}`,
      confidence: 85,
      insights: ['Key insight 1', 'Key insight 2']
    };
  }

  private executeConditionNode(node: ChainNode, inputs: Record<string, any>): any {
    // Simple condition evaluation
    const condition = node.data.content || 'true';
    const input = inputs.input;
    
    try {
      // Very basic evaluation - in production, use a safe expression evaluator
      const result = eval(condition.replace(/input/g, JSON.stringify(input)));
      return {
        true: result ? input : undefined,
        false: result ? undefined : input
      };
    } catch (error) {
      return {
        true: undefined,
        false: input
      };
    }
  }

  private executeOutputNode(node: ChainNode, inputs: Record<string, any>): any {
    const format = node.data.config?.format || 'json';
    const includeMetadata = node.data.config?.includeMetadata || false;
    
    let output = inputs.data;
    
    if (includeMetadata) {
      output = {
        data: output,
        metadata: {
          timestamp: new Date().toISOString(),
          format,
          nodeId: node.id
        }
      };
    }
    
    return { output };
  }

  /**
   * Validate a flow
   */
  validateFlow(flow: ChainFlow): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Check for orphaned nodes
    const connectedNodes = new Set([
      ...flow.connections.map(c => c.sourceNodeId),
      ...flow.connections.map(c => c.targetNodeId)
    ]);

    const orphanedNodes = flow.nodes.filter(node => 
      node.type !== 'input' && node.type !== 'output' && !connectedNodes.has(node.id)
    );

    if (orphanedNodes.length > 0) {
      errors.push(`Orphaned nodes found: ${orphanedNodes.map(n => n.data.title).join(', ')}`);
    }

    // Check for required inputs
    flow.nodes.forEach(node => {
      const requiredInputs = node.inputs.filter(input => input.required);
      const connectedInputs = flow.connections
        .filter(conn => conn.targetNodeId === node.id)
        .map(conn => conn.targetInputId);

      const missingInputs = requiredInputs.filter(input => 
        !connectedInputs.includes(input.id)
      );

      if (missingInputs.length > 0) {
        errors.push(`Node "${node.data.title}" missing required inputs: ${missingInputs.map(i => i.label).join(', ')}`);
      }
    });

    // Check for cycles
    if (this.hasCycles(flow)) {
      errors.push('Flow contains cycles');
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  private hasCycles(flow: ChainFlow): boolean {
    const visited = new Set<string>();
    const recursionStack = new Set<string>();

    const hasCycle = (nodeId: string): boolean => {
      if (recursionStack.has(nodeId)) return true;
      if (visited.has(nodeId)) return false;

      visited.add(nodeId);
      recursionStack.add(nodeId);

      const outgoingConnections = flow.connections.filter(conn => conn.sourceNodeId === nodeId);
      for (const conn of outgoingConnections) {
        if (hasCycle(conn.targetNodeId)) return true;
      }

      recursionStack.delete(nodeId);
      return false;
    };

    for (const node of flow.nodes) {
      if (!visited.has(node.id) && hasCycle(node.id)) {
        return true;
      }
    }

    return false;
  }
}

export const chainLinkerService = new ChainLinkerService();
