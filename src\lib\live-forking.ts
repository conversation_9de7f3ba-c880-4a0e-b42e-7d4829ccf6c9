import { PromptVariant } from '@/store/promptStore';

export interface ForkableResult {
  id: string;
  type: 'prompt_test' | 'agent_simulation' | 'variation_testing' | 'project_simulation';
  timestamp: Date;
  content: {
    prompt?: string;
    systemPrompt?: string;
    response?: string;
    variables?: Record<string, string>;
    model?: string;
    provider?: string;
    score?: any;
  };
  metadata: {
    originalId: string;
    parentType: string;
    context: Record<string, any>;
  };
}

export interface ForkConfiguration {
  name: string;
  purpose: string;
  modifications: {
    prompt?: string;
    systemPrompt?: string;
    variables?: Record<string, string>;
    temperature?: number;
    maxTokens?: number;
  };
  preserveOriginal: boolean;
  autoTest: boolean;
}

export interface ForkResult {
  success: boolean;
  variantId?: string;
  variant?: PromptVariant;
  error?: string;
  testResults?: any;
}

class LiveForkingService {
  /**
   * Extract forkable content from various result types
   */
  extractForkableContent(result: any): ForkableResult | null {
    try {
      switch (result.type) {
        case 'prompt_test':
          return this.extractFromPromptTest(result);
        case 'agent_simulation':
          return this.extractFromAgentSimulation(result);
        case 'variation_testing':
          return this.extractFromVariationTesting(result);
        case 'project_simulation':
          return this.extractFromProjectSimulation(result);
        default:
          return null;
      }
    } catch (error) {
      console.error('Failed to extract forkable content:', error);
      return null;
    }
  }

  private extractFromPromptTest(result: any): ForkableResult {
    // Extract the best performing result or the first one
    const bestResult = result.results?.find((r: any) => !r.error) || result.results?.[0];
    
    return {
      id: `fork_${Date.now()}`,
      type: 'prompt_test',
      timestamp: result.timestamp,
      content: {
        prompt: result.prompt,
        systemPrompt: result.systemPrompt,
        response: bestResult?.response,
        variables: result.variables,
        model: bestResult?.model,
        provider: bestResult?.provider,
        score: bestResult?.score
      },
      metadata: {
        originalId: result.id,
        parentType: 'prompt_test',
        context: {
          totalResults: result.results?.length || 0,
          summary: result.summary
        }
      }
    };
  }

  private extractFromAgentSimulation(result: any): ForkableResult {
    // Extract the scenario and best agent response
    const bestAgent = result.data?.agents?.find((a: any) => a.confidence > 80) || result.data?.agents?.[0];
    
    return {
      id: `fork_${Date.now()}`,
      type: 'agent_simulation',
      timestamp: result.timestamp,
      content: {
        prompt: result.data?.scenario,
        response: bestAgent?.response,
        variables: {},
        score: {
          confidence: bestAgent?.confidence,
          sentiment: bestAgent?.sentiment
        }
      },
      metadata: {
        originalId: result.timestamp?.getTime?.()?.toString() || Date.now().toString(),
        parentType: 'agent_simulation',
        context: {
          scenario: result.data?.scenario,
          agentCount: result.data?.agents?.length || 0,
          summary: result.data?.summary
        }
      }
    };
  }

  private extractFromVariationTesting(result: any): ForkableResult {
    // Extract the best performing variation
    const bestVariation = result.data?.variations?.reduce((best: any, current: any) => {
      const currentScore = current.aggregatedScores?.overallScore || 0;
      const bestScore = best?.aggregatedScores?.overallScore || 0;
      return currentScore > bestScore ? current : best;
    }, null);

    return {
      id: `fork_${Date.now()}`,
      type: 'variation_testing',
      timestamp: result.timestamp,
      content: {
        prompt: bestVariation?.prompt,
        variables: bestVariation?.variables,
        response: bestVariation?.results?.[0]?.response,
        score: bestVariation?.aggregatedScores
      },
      metadata: {
        originalId: result.timestamp?.getTime?.()?.toString() || Date.now().toString(),
        parentType: 'variation_testing',
        context: {
          totalVariations: result.data?.variations?.length || 0,
          comparison: result.data?.comparison
        }
      }
    };
  }

  private extractFromProjectSimulation(result: any): ForkableResult {
    // Extract successful project steps as a combined prompt
    const successfulSteps = result.data?.steps?.filter((s: any) => s.status === 'completed') || [];
    const combinedPrompt = successfulSteps.map((s: any) => `${s.name}: ${s.description}`).join('\n');
    
    return {
      id: `fork_${Date.now()}`,
      type: 'project_simulation',
      timestamp: result.timestamp,
      content: {
        prompt: combinedPrompt,
        response: result.data?.insights?.recommendations?.join('\n'),
        score: {
          successRate: result.data?.metrics?.successRate,
          quality: result.data?.metrics?.overallQuality
        }
      },
      metadata: {
        originalId: result.timestamp?.getTime?.()?.toString() || Date.now().toString(),
        parentType: 'project_simulation',
        context: {
          projectName: result.data?.project?.name,
          totalSteps: result.data?.steps?.length || 0,
          insights: result.data?.insights
        }
      }
    };
  }

  /**
   * Create a new prompt variant from forkable content
   */
  async forkToVariant(
    forkableContent: ForkableResult,
    configuration: ForkConfiguration,
    addToStore: (variant: PromptVariant) => void
  ): Promise<ForkResult> {
    try {
      // Create the new variant
      const variant: PromptVariant = {
        id: `variant_${Date.now()}`,
        name: configuration.name,
        prompt: configuration.modifications.prompt || forkableContent.content.prompt || '',
        systemPrompt: configuration.modifications.systemPrompt || forkableContent.content.systemPrompt,
        variables: { ...forkableContent.content.variables, ...configuration.modifications.variables },
        purpose: configuration.purpose,
        tags: [
          'forked',
          forkableContent.type,
          forkableContent.metadata.parentType
        ],
        metadata: {
          forkedFrom: forkableContent.metadata.originalId,
          forkedAt: new Date().toISOString(),
          originalType: forkableContent.type,
          preservedOriginal: configuration.preserveOriginal,
          modifications: Object.keys(configuration.modifications).filter(key => 
            configuration.modifications[key as keyof typeof configuration.modifications] !== undefined
          )
        }
      };

      // Add to store
      addToStore(variant);

      // Auto-test if requested
      let testResults;
      if (configuration.autoTest) {
        testResults = await this.autoTestVariant(variant, configuration);
      }

      return {
        success: true,
        variantId: variant.id,
        variant,
        testResults
      };
    } catch (error) {
      console.error('Failed to fork variant:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      };
    }
  }

  private async autoTestVariant(variant: PromptVariant, configuration: ForkConfiguration): Promise<any> {
    // This would integrate with the prompt testing service
    // For now, return a mock result
    return {
      tested: true,
      timestamp: new Date(),
      configuration: {
        temperature: configuration.modifications.temperature || 0.7,
        maxTokens: configuration.modifications.maxTokens || 1024
      }
    };
  }

  /**
   * Generate suggested modifications based on the original content and scores
   */
  generateSuggestedModifications(forkableContent: ForkableResult): Partial<ForkConfiguration['modifications']> {
    const suggestions: Partial<ForkConfiguration['modifications']> = {};

    // Analyze scores to suggest improvements
    if (forkableContent.content.score) {
      const score = forkableContent.content.score;

      // Suggest prompt improvements based on low scores
      if (score.fidelity < 70) {
        suggestions.prompt = this.suggestFidelityImprovement(forkableContent.content.prompt);
      }

      if (score.creativity < 60) {
        suggestions.temperature = 0.9; // Increase creativity
      }

      if (score.consistency < 70) {
        suggestions.temperature = 0.3; // Decrease for more consistency
      }

      if (score.adherence < 70 && forkableContent.content.systemPrompt) {
        suggestions.systemPrompt = this.suggestAdherenceImprovement(forkableContent.content.systemPrompt);
      }
    }

    return suggestions;
  }

  private suggestFidelityImprovement(originalPrompt?: string): string {
    if (!originalPrompt) return '';

    // Add specificity and clarity
    const improvements = [
      'Be specific and detailed in your response.',
      'Provide concrete examples where applicable.',
      'Structure your answer clearly with main points.',
      'Ensure your response directly addresses the question.'
    ];

    return `${originalPrompt}\n\n${improvements[Math.floor(Math.random() * improvements.length)]}`;
  }

  private suggestAdherenceImprovement(originalSystemPrompt?: string): string {
    if (!originalSystemPrompt) return '';

    // Strengthen instruction following
    const strengtheners = [
      'Follow the instructions precisely.',
      'Maintain the specified format throughout your response.',
      'Pay careful attention to the tone and style requirements.',
      'Ensure all specified elements are included in your response.'
    ];

    return `${originalSystemPrompt}\n\n${strengtheners[Math.floor(Math.random() * strengtheners.length)]}`;
  }

  /**
   * Get fork history for a specific result
   */
  getForkHistory(originalId: string): ForkableResult[] {
    // This would query the store for all variants forked from this result
    // For now, return empty array
    return [];
  }

  /**
   * Validate fork configuration
   */
  validateForkConfiguration(config: ForkConfiguration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!config.name.trim()) {
      errors.push('Variant name is required');
    }

    if (!config.purpose.trim()) {
      errors.push('Purpose description is required');
    }

    if (config.name.length > 100) {
      errors.push('Variant name must be less than 100 characters');
    }

    if (config.purpose.length > 500) {
      errors.push('Purpose description must be less than 500 characters');
    }

    // Validate modifications
    if (config.modifications.temperature !== undefined) {
      if (config.modifications.temperature < 0 || config.modifications.temperature > 2) {
        errors.push('Temperature must be between 0 and 2');
      }
    }

    if (config.modifications.maxTokens !== undefined) {
      if (config.modifications.maxTokens < 1 || config.modifications.maxTokens > 4096) {
        errors.push('Max tokens must be between 1 and 4096');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Generate a default fork name based on the original content
   */
  generateDefaultForkName(forkableContent: ForkableResult): string {
    const timestamp = new Date().toLocaleString();
    const type = forkableContent.type.replace('_', ' ');
    
    switch (forkableContent.type) {
      case 'prompt_test':
        return `Forked Prompt - ${timestamp}`;
      case 'agent_simulation':
        return `Agent Response Fork - ${timestamp}`;
      case 'variation_testing':
        return `Best Variation Fork - ${timestamp}`;
      case 'project_simulation':
        return `Project Steps Fork - ${timestamp}`;
      default:
        return `Forked ${type} - ${timestamp}`;
    }
  }

  /**
   * Generate a default purpose based on the original content and scores
   */
  generateDefaultPurpose(forkableContent: ForkableResult): string {
    const score = forkableContent.content.score;
    
    if (score) {
      const lowScores = Object.entries(score)
        .filter(([_, value]) => typeof value === 'number' && value < 70)
        .map(([key, _]) => key);

      if (lowScores.length > 0) {
        return `Improve ${lowScores.join(', ')} based on original ${forkableContent.type.replace('_', ' ')} results`;
      }
    }

    return `Enhanced version of ${forkableContent.type.replace('_', ' ')} with modifications`;
  }
}

export const liveForkingService = new LiveForkingService();
