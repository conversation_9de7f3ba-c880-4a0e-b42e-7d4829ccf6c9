
import { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { PromptTester } from '@/components/PromptTester';
import { ResultsDashboard } from '@/components/ResultsDashboard';
import { PromptHistory } from '@/components/PromptHistory';
import { Settings } from '@/components/Settings';
import { AgentSimulator } from '@/components/AgentSimulator';
import { PromptVariations } from '@/components/PromptVariations';
import { ProjectSimulator } from '@/components/ProjectSimulator';
import { ChainLinkerCanvas } from '@/components/ChainLinkerCanvas';
import { OnboardingTour, useOnboarding } from '@/components/OnboardingTour';
import { usePromptStore } from '@/hooks/usePromptStore';
import { agentSimulationService } from '@/lib/agent-simulation';
import { promptVariationsTestingService } from '@/lib/prompt-variations-testing';
import { Agent, PromptVariant } from '@/store/promptStore';
import { toast } from '@/hooks/use-toast';

const Index = () => {
  const [activeView, setActiveView] = useState('tester');
  const [error, setError] = useState<string | null>(null);

  // Wrap usePromptStore in try-catch
  let storeData;
  try {
    storeData = usePromptStore();
  } catch (err) {
    console.error('Store error:', err);
    setError(err instanceof Error ? err.message : 'Store initialization failed');
  }

  const { results, addResult, runIntegratedTest, initialized } = storeData || {};

  // Wrap useOnboarding in try-catch
  let onboardingData;
  try {
    onboardingData = useOnboarding();
  } catch (err) {
    console.error('Onboarding error:', err);
  }

  const { showOnboarding, closeOnboarding } = onboardingData || { showOnboarding: false, closeOnboarding: () => {} };

  // Show error state
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-red-400 text-6xl mb-4">⚠️</div>
          <h2 className="text-xl font-semibold text-slate-200 mb-2">Initialization Error</h2>
          <p className="text-slate-400 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  // Show loading state while initializing
  if (!initialized) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-400 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-slate-200">Loading Prompt Studio...</h2>
          <p className="text-slate-400 mt-2">Initializing your workspace</p>
        </div>
      </div>
    );
  }

  const handleNewResults = (newResults: any) => {
    addResult({
      timestamp: new Date(),
      type: 'prompt_test',
      data: newResults
    });
  };

  const handleAgentSimulation = async (agents: Agent[], scenario: string) => {
    if (!scenario.trim()) {
      toast({
        title: "Error",
        description: "Please provide a scenario for simulation",
        variant: "destructive"
      });
      return;
    }

    if (agents.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one agent for simulation",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Simulation Started",
      description: `Running simulation with ${agents.length} agent(s)...`,
    });

    try {
      const simulationResult = await agentSimulationService.runSimulation({
        agents,
        scenario,
        temperature: 0.7,
        maxTokens: 1024
      });

      // Convert simulation result to the format expected by the store
      const storeResult = {
        timestamp: simulationResult.timestamp,
        type: 'agent_simulation' as const,
        data: {
          scenario: simulationResult.scenario,
          agents: simulationResult.agentResponses.map(response => ({
            id: response.agentId,
            name: response.agentName,
            role: agents.find(a => a.id === response.agentId)?.role || 'Unknown',
            avatar: agents.find(a => a.id === response.agentId)?.avatar || '🤖',
            response: response.response,
            sentiment: response.sentiment,
            confidence: response.confidence,
            keyInsights: response.keyInsights,
            reasoning: response.reasoning
          })),
          summary: simulationResult.summary,
          metadata: {
            totalAgents: agents.length,
            averageConfidence: simulationResult.metadata.averageConfidence,
            simulationDuration: simulationResult.metadata.duration,
            totalTokens: simulationResult.metadata.totalTokens
          }
        },
        scores: {
          fidelity: simulationResult.metadata.averageConfidence,
          adherence: Math.min(95, simulationResult.metadata.averageConfidence + 10),
          consistency: Math.max(60, simulationResult.metadata.averageConfidence - 5),
          creativity: Math.floor(Math.random() * 40) + 60,
          accuracy: simulationResult.metadata.averageConfidence
        }
      };

      addResult(storeResult);

      toast({
        title: "Simulation Complete",
        description: `Generated responses from ${agents.length} agent(s) in ${(simulationResult.metadata.duration / 1000).toFixed(1)}s`,
      });
    } catch (error) {
      console.error('Agent simulation failed:', error);
      toast({
        title: "Simulation Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const handleVariationTesting = async (variations: PromptVariant[]) => {
    if (variations.length === 0) {
      toast({
        title: "Error",
        description: "No variations to test",
        variant: "destructive"
      });
      return;
    }

    toast({
      title: "Variation Testing Started",
      description: `Testing ${variations.length} prompt variation(s)...`,
    });

    try {
      // Use default models for testing (OpenAI and Anthropic)
      const defaultModels = ['openai:gpt-4', 'anthropic:claude-3-sonnet'];

      const testResult = await promptVariationsTestingService.runVariationTests({
        variants: variations,
        models: defaultModels,
        temperature: 0.7,
        maxTokens: 1024
      });

      // Convert test result to the format expected by the store
      const storeResult = {
        timestamp: testResult.timestamp,
        type: 'variation_testing' as const,
        data: {
          packInfo: testResult.packInfo,
          variations: testResult.variants.map(variantResult => ({
            id: variantResult.variant.id,
            name: variantResult.variant.name,
            prompt: variantResult.variant.prompt,
            variables: variantResult.variant.variables,
            purpose: variantResult.variant.purpose,
            results: variantResult.testResults.flatMap(testResult =>
              testResult.results.map(result => ({
                model: result.model,
                response: result.response,
                score: result.score,
                timeToCompletion: result.timeToCompletion,
                tokens: result.tokens.total,
                error: result.error,
                qualityMetrics: {
                  clarity: result.score.fidelity,
                  relevance: result.score.adherence,
                  completeness: result.score.consistency
                }
              }))
            ),
            aggregatedScores: variantResult.aggregatedScores,
            performance: variantResult.performance
          })),
          comparison: testResult.comparison,
          statistics: testResult.statistics
        },
        scores: {
          fidelity: testResult.statistics.successRate,
          adherence: testResult.variants.reduce((sum, v) => sum + v.aggregatedScores.averageAdherence, 0) / testResult.variants.length,
          consistency: testResult.variants.reduce((sum, v) => sum + v.aggregatedScores.averageConsistency, 0) / testResult.variants.length,
          creativity: testResult.variants.reduce((sum, v) => sum + v.aggregatedScores.averageCreativity, 0) / testResult.variants.length,
          accuracy: testResult.variants.reduce((sum, v) => sum + v.aggregatedScores.overallScore, 0) / testResult.variants.length
        }
      };

      addResult(storeResult);

      toast({
        title: "Variation Testing Complete",
        description: `Tested ${variations.length} variations across ${testResult.metadata.modelsUsed.length} models in ${(testResult.metadata.duration / 1000).toFixed(1)}s`,
      });
    } catch (error) {
      console.error('Variation testing failed:', error);
      toast({
        title: "Variation Testing Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    }
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'tester':
        return <PromptTester onResults={handleNewResults} />;
      case 'results':
        return <ResultsDashboard results={results} />;
      case 'history':
        return <PromptHistory />;
      case 'agents':
        return <AgentSimulator onRunSimulation={handleAgentSimulation} />;
      case 'variations':
        return <PromptVariations onRunVariations={handleVariationTesting} />;
      case 'projects':
        return <ProjectSimulator />;
      case 'chain-linker':
        return <ChainLinkerCanvas />;
      case 'settings':
        return <Settings />;
      default:
        return <PromptTester onResults={handleNewResults} />;
    }
  };

  try {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
        <div className="flex">
          <Sidebar activeView={activeView} onViewChange={setActiveView} />
          <main className="flex-1 p-6">
            <div className="max-w-7xl mx-auto">
              <header className="mb-8">
                <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  💠 Prompt Studio
                </h1>
                <p className="text-slate-400 mt-2">
                  Unified workspace for prompt engineering, agent simulation, and integrated testing
                </p>
              </header>
              {renderActiveView()}
            </div>
          </main>
        </div>

        <OnboardingTour
          isOpen={showOnboarding}
          onClose={closeOnboarding}
          onViewChange={setActiveView}
        />
      </div>
    );
  } catch (renderError) {
    console.error('Render error:', renderError);
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 flex items-center justify-center">
        <div className="text-center max-w-md">
          <div className="text-yellow-400 text-6xl mb-4">🔧</div>
          <h2 className="text-xl font-semibold text-slate-200 mb-2">Render Error</h2>
          <p className="text-slate-400 mb-4">
            {renderError instanceof Error ? renderError.message : 'Component render failed'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }
};

export default Index;
