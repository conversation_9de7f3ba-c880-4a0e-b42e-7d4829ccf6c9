import { useState } from 'react';
// import { Sidebar } from '@/components/Sidebar';
// import { usePromptStore } from '@/hooks/usePromptStore';
// import { Button } from '@/components/ui/button';
// Temporarily commented out to isolate the issue
// import { PromptTester } from '@/components/PromptTester';
// import { PromptVariations } from '@/components/PromptVariations';
// import { AgentSimulator } from '@/components/AgentSimulator';
// import { ProjectSimulator } from '@/components/ProjectSimulator';
// import { ChainLinkerCanvas } from '@/components/ChainLinkerCanvas';
// import { ResultsDashboard } from '@/components/ResultsDashboard';
// import { PromptHistory } from '@/components/PromptHistory';
// import { Settings } from '@/components/Settings';

const Index = () => {
  console.log("🏠 Index component mounting...");
  const [activeView, setActiveView] = useState('tester');
  console.log("📍 Active view set to:", activeView);

  // Temporarily removed store initialization
  const initialized = true;
  console.log("🔧 Simplified mode, initialized:", initialized);

  const handleResults = (results: any) => {
    if (addResult) {
      addResult(results);
    }
  };

  const handleRunVariations = (variants: any[]) => {
    console.log('Running variations:', variants);
    // Implementation for running variations
  };

  const handleRunSimulation = (agents: any[], scenario: string) => {
    console.log('Running simulation:', agents, scenario);
    // Implementation for running simulation
  };

  const renderActiveView = () => {
    console.log('🎨 Rendering active view:', activeView);

    // Fallback diagnostic view first
    const fallbackView = (
      <div className="p-8 bg-slate-800 rounded-lg border border-slate-700">
        <h2 className="text-2xl font-bold text-green-400 mb-4">🔧 Diagnostic Mode</h2>
        <div className="space-y-2 text-slate-300">
          <p>✅ App mounted successfully</p>
          <p>✅ Index component rendered</p>
          <p>📍 Active view: <span className="text-blue-400 font-semibold">{activeView}</span></p>
          <p>🔧 Store initialized: <span className="text-green-400">{initialized ? 'Yes' : 'No'}</span></p>
          <p>🕒 Timestamp: {new Date().toLocaleTimeString()}</p>
        </div>
        <div className="mt-4 p-4 bg-slate-900/50 rounded border border-slate-600">
          <p className="text-slate-400 text-sm">
            This fallback view confirms the basic React rendering pipeline is working.
            If you see this, the issue is likely with component imports or dependencies.
          </p>
        </div>
      </div>
    );

    // For now, return fallback to test basic rendering
    return fallbackView;

    /* Commented out for debugging
    try {
      switch (activeView) {
        case 'tester':
          console.log('🧪 Loading PromptTester...');
          return <PromptTester onResults={handleResults} />;
        case 'variations':
          console.log('🔀 Loading PromptVariations...');
          return <PromptVariations onRunVariations={handleRunVariations} />;
        case 'agents':
          console.log('🤖 Loading AgentSimulator...');
          return <AgentSimulator onRunSimulation={handleRunSimulation} />;
        case 'projects':
          console.log('📁 Loading ProjectSimulator...');
          return <ProjectSimulator />;
        case 'chain-linker':
          console.log('🔗 Loading ChainLinkerCanvas...');
          return <ChainLinkerCanvas />;
        case 'results':
          console.log('📊 Loading ResultsDashboard...');
          return <ResultsDashboard />;
        case 'history':
          console.log('📚 Loading PromptHistory...');
          return <PromptHistory />;
        case 'settings':
          console.log('⚙️ Loading Settings...');
          return <Settings />;
        default:
          console.log('🧪 Loading default PromptTester...');
          return <PromptTester onResults={handleResults} />;
      }
    } catch (error) {
      console.error('❌ Error rendering view:', error);
      return (
        <div className="p-8 text-center">
          <h2 className="text-2xl font-bold text-red-400 mb-4">Component Error</h2>
          <p className="text-slate-300 mb-2">
            Failed to load the {activeView} component.
          </p>
          <p className="text-slate-400 text-sm">
            Error: {error?.toString()}
          </p>
          <div className="mt-4">
            <Button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700"
            >
              Reload Page
            </Button>
          </div>
        </div>
      );
    }
    */
  };

  console.log('🎯 Index component about to render JSX, activeView:', activeView, 'initialized:', initialized);

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      <div className="p-6">
        <header className="mb-8">
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
            💠 Prompt Studio
          </h1>
          <p className="text-slate-400 mt-2">
            Unified workspace for prompt engineering, agent simulation, and integrated testing
          </p>
        </header>

        {renderActiveView()}
      </div>
    </div>
  );
};

export default Index;
