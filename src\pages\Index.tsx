import { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { usePromptStore } from '@/hooks/usePromptStore';
import { PromptTester } from '@/components/PromptTester';
import { PromptVariations } from '@/components/PromptVariations';
import { AgentSimulator } from '@/components/AgentSimulator';
import { ProjectSimulator } from '@/components/ProjectSimulator';
import { ChainLinkerCanvas } from '@/components/ChainLinkerCanvas';
import { ResultsDashboard } from '@/components/ResultsDashboard';
import { PromptHistory } from '@/components/PromptHistory';
import { Settings } from '@/components/Settings';

const Index = () => {
  const [activeView, setActiveView] = useState('tester');

  // Initialize store with error handling
  let storeData;
  try {
    storeData = usePromptStore();
    console.log('Store initialized successfully');
  } catch (err) {
    console.error('Store initialization failed:', err);
    // Continue with fallback
  }

  const { initialized, addResult } = storeData || { initialized: false, addResult: () => {} };

  const handleResults = (results: any) => {
    if (addResult) {
      addResult(results);
    }
  };

  const handleRunVariations = (variants: any[]) => {
    console.log('Running variations:', variants);
    // Implementation for running variations
  };

  const handleRunSimulation = (agents: any[], scenario: string) => {
    console.log('Running simulation:', agents, scenario);
    // Implementation for running simulation
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'tester':
        return <PromptTester onResults={handleResults} />;
      case 'variations':
        return <PromptVariations onRunVariations={handleRunVariations} />;
      case 'agents':
        return <AgentSimulator onRunSimulation={handleRunSimulation} />;
      case 'projects':
        return <ProjectSimulator />;
      case 'chain-linker':
        return <ChainLinkerCanvas />;
      case 'results':
        return <ResultsDashboard />;
      case 'history':
        return <PromptHistory />;
      case 'settings':
        return <Settings />;
      default:
        return <PromptTester onResults={handleResults} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      <div className="flex">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            <header className="mb-8">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                💠 Prompt Studio
              </h1>
              <p className="text-slate-400 mt-2">
                Unified workspace for prompt engineering, agent simulation, and integrated testing
              </p>
            </header>

            {renderActiveView()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
