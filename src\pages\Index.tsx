
import { useState } from 'react';
import { Sidebar } from '@/components/Sidebar';
import { PromptTester } from '@/components/PromptTester';
import { ResultsDashboard } from '@/components/ResultsDashboard';
import { PromptHistory } from '@/components/PromptHistory';
import { Settings } from '@/components/Settings';
import { AgentSimulator } from '@/components/AgentSimulator';
import { PromptVariations } from '@/components/PromptVariations';
import { ProjectSimulator } from '@/components/ProjectSimulator';
import { usePromptStore } from '@/hooks/usePromptStore';

const Index = () => {
  const [activeView, setActiveView] = useState('tester');
  const { results, addResult, runIntegratedTest } = usePromptStore();

  const handleNewResults = (newResults: any) => {
    addResult({
      timestamp: new Date(),
      type: 'prompt_test',
      data: newResults
    });
  };

  const handleAgentSimulation = async (agents: any[], scenario: string) => {
    // Create enhanced simulation result
    const mockResults = {
      timestamp: new Date(),
      type: 'agent_simulation' as const,
      data: {
        scenario,
        agents: agents.map(agent => ({
          id: agent.id,
          name: agent.name,
          role: agent.role,
          avatar: agent.avatar,
          response: `As a ${agent.role}, I believe this scenario requires careful consideration of ${scenario.slice(0, 50)}... My expertise in ${agent.expertise?.[0] || 'general analysis'} suggests we should focus on...`,
          sentiment: Math.random() > 0.5 ? 'positive' : 'constructive',
          confidence: Math.floor(Math.random() * 40) + 60, // 60-100%
          keyInsights: [
            `Primary concern: ${agent.role} perspective on implementation`,
            `Risk assessment: ${Math.random() > 0.5 ? 'Low' : 'Medium'} complexity`,
            `Recommendation: Further ${agent.expertise?.[0] || 'analysis'} needed`
          ]
        }))
      },
      scores: {
        fidelity: Math.floor(Math.random() * 30) + 70,
        adherence: Math.floor(Math.random() * 25) + 75,
        consistency: Math.floor(Math.random() * 20) + 80,
        creativity: Math.floor(Math.random() * 40) + 60,
        accuracy: Math.floor(Math.random() * 35) + 65
      }
    };

    addResult(mockResults);
  };

  const handleVariationTesting = async (variations: any[]) => {
    // Create enhanced variation testing result
    const mockResults = {
      timestamp: new Date(),
      type: 'variation_testing' as const,
      data: {
        packInfo: {
          title: 'Test Prompt Pack',
          totalVariations: variations.length,
          timestamp: new Date()
        },
        variations: variations.map(variation => ({
          id: variation.id,
          name: variation.name,
          prompt: variation.prompt,
          variables: variation.variables,
          purpose: variation.purpose,
          results: [
            {
              model: 'GPT-4',
              response: `Enhanced response for ${variation.name}: ${variation.prompt.slice(0, 100)}...`,
              score: {
                fidelity: Math.floor(Math.random() * 30) + 70,
                adherence: Math.floor(Math.random() * 25) + 75,
                consistency: Math.floor(Math.random() * 20) + 80,
                creativity: Math.floor(Math.random() * 40) + 60
              },
              timeToCompletion: Math.floor(Math.random() * 2000) + 1000,
              tokens: Math.floor(Math.random() * 400) + 200,
              qualityMetrics: {
                clarity: Math.floor(Math.random() * 30) + 70,
                relevance: Math.floor(Math.random() * 25) + 75,
                completeness: Math.floor(Math.random() * 35) + 65
              }
            }
          ]
        }))
      },
      scores: {
        fidelity: Math.floor(Math.random() * 30) + 70,
        adherence: Math.floor(Math.random() * 25) + 75,
        consistency: Math.floor(Math.random() * 20) + 80,
        creativity: Math.floor(Math.random() * 40) + 60,
        accuracy: Math.floor(Math.random() * 35) + 65
      }
    };

    addResult(mockResults);
  };

  const renderActiveView = () => {
    switch (activeView) {
      case 'tester':
        return <PromptTester onResults={handleNewResults} />;
      case 'results':
        return <ResultsDashboard results={results} />;
      case 'history':
        return <PromptHistory />;
      case 'agents':
        return <AgentSimulator onRunSimulation={handleAgentSimulation} />;
      case 'variations':
        return <PromptVariations onRunVariations={handleVariationTesting} />;
      case 'projects':
        return <ProjectSimulator />;
      case 'settings':
        return <Settings />;
      default:
        return <PromptTester onResults={handleNewResults} />;
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      <div className="flex">
        <Sidebar activeView={activeView} onViewChange={setActiveView} />
        <main className="flex-1 p-6">
          <div className="max-w-7xl mx-auto">
            <header className="mb-8">
              <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                💠 Prompt Studio
              </h1>
              <p className="text-slate-400 mt-2">
                Unified workspace for prompt engineering, agent simulation, and integrated testing
              </p>
            </header>
            {renderActiveView()}
          </div>
        </main>
      </div>
    </div>
  );
};

export default Index;
