import { useState } from 'react';

const Index = () => {
  const [activeView, setActiveView] = useState('tester');
  
  // Simplified version for debugging
  console.log('Index component rendering...');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white">
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center max-w-4xl mx-auto p-8">
          <h1 className="text-6xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-6">
            💠 Prompt Studio
          </h1>
          <p className="text-2xl text-slate-300 mb-8">
            Unified workspace for prompt engineering, agent simulation, and integrated testing
          </p>
          
          <div className="bg-slate-800/50 p-8 rounded-lg border border-slate-700 mb-8">
            <h2 className="text-2xl font-bold text-green-400 mb-4">🎉 Application Successfully Loaded!</h2>
            <div className="grid grid-cols-2 gap-4 text-lg">
              <div className="bg-slate-900/50 p-4 rounded">
                <span className="text-green-400">✅ React</span> - Working
              </div>
              <div className="bg-slate-900/50 p-4 rounded">
                <span className="text-green-400">✅ TypeScript</span> - Working
              </div>
              <div className="bg-slate-900/50 p-4 rounded">
                <span className="text-green-400">✅ Vite</span> - Working
              </div>
              <div className="bg-slate-900/50 p-4 rounded">
                <span className="text-green-400">✅ Tailwind</span> - Working
              </div>
            </div>
          </div>
          
          <div className="bg-blue-900/30 p-6 rounded-lg border border-blue-500/30">
            <h3 className="text-xl font-semibold text-blue-300 mb-3">🔧 Debug Mode Active</h3>
            <p className="text-slate-300">
              This is a simplified version to test basic functionality. 
              The full component suite will be restored once we confirm this works.
            </p>
            <p className="text-slate-400 mt-2">
              Current view: <span className="text-blue-400 font-semibold">{activeView}</span>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Index;
