import { PromptVariant, Agent } from '@/store/promptStore';

export interface ExportFormat {
  id: string;
  name: string;
  extension: string;
  description: string;
  mimeType: string;
}

export interface ExportConfiguration {
  format: string;
  includeMetadata: boolean;
  includeResults: boolean;
  includeAgents: boolean;
  includeVariables: boolean;
  dateRange?: {
    start: Date;
    end: Date;
  };
  filters?: {
    types: string[];
    tags: string[];
    minScore?: number;
  };
}

export interface ExportData {
  prompts?: PromptVariant[];
  agents?: Agent[];
  results?: any[];
  metadata?: {
    exportedAt: Date;
    version: string;
    source: string;
    configuration: ExportConfiguration;
  };
}

export const EXPORT_FORMATS: Record<string, ExportFormat> = {
  promptx: {
    id: 'promptx',
    name: 'PromptX Format',
    extension: '.promptx',
    description: 'Specialized format for prompt engineering tools',
    mimeType: 'application/json'
  },
  json: {
    id: 'json',
    name: '<PERSON><PERSON><PERSON>',
    extension: '.json',
    description: 'Standard JSON format for data interchange',
    mimeType: 'application/json'
  },
  csv: {
    id: 'csv',
    name: 'CSV',
    extension: '.csv',
    description: 'Comma-separated values for spreadsheet applications',
    mimeType: 'text/csv'
  },
  markdown: {
    id: 'markdown',
    name: 'Markdown',
    extension: '.md',
    description: 'Human-readable documentation format',
    mimeType: 'text/markdown'
  },
  yaml: {
    id: 'yaml',
    name: 'YAML',
    extension: '.yaml',
    description: 'Human-readable data serialization standard',
    mimeType: 'text/yaml'
  }
};

class ExportToolsService {
  /**
   * Export data in the specified format
   */
  async exportData(data: ExportData, configuration: ExportConfiguration): Promise<Blob> {
    const format = EXPORT_FORMATS[configuration.format];
    if (!format) {
      throw new Error(`Unsupported export format: ${configuration.format}`);
    }

    // Filter and prepare data based on configuration
    const filteredData = this.filterData(data, configuration);

    switch (configuration.format) {
      case 'promptx':
        return this.exportToPromptX(filteredData, configuration);
      case 'json':
        return this.exportToJSON(filteredData, configuration);
      case 'csv':
        return this.exportToCSV(filteredData, configuration);
      case 'markdown':
        return this.exportToMarkdown(filteredData, configuration);
      case 'yaml':
        return this.exportToYAML(filteredData, configuration);
      default:
        throw new Error(`Export format ${configuration.format} not implemented`);
    }
  }

  /**
   * Export to PromptX format (specialized for prompt engineering)
   */
  private exportToPromptX(data: ExportData, config: ExportConfiguration): Blob {
    const promptxData = {
      version: '1.0',
      format: 'promptx',
      metadata: {
        ...data.metadata,
        schema: 'https://promptx.dev/schema/v1',
        tools: ['Prompt Studio'],
        capabilities: ['prompt_testing', 'agent_simulation', 'variation_testing']
      },
      prompts: data.prompts?.map(prompt => ({
        id: prompt.id,
        name: prompt.name,
        content: {
          prompt: prompt.prompt,
          systemPrompt: prompt.systemPrompt,
          variables: prompt.variables
        },
        metadata: {
          purpose: prompt.purpose,
          tags: prompt.tags,
          created: prompt.metadata?.forkedAt || new Date().toISOString(),
          version: '1.0'
        },
        testing: {
          supported_models: ['gpt-4', 'claude-3-sonnet'],
          parameters: {
            temperature: { min: 0, max: 2, default: 0.7 },
            max_tokens: { min: 1, max: 4096, default: 1024 }
          }
        }
      })),
      agents: data.agents?.map(agent => ({
        id: agent.id,
        name: agent.name,
        role: agent.role,
        personality: agent.personality,
        expertise: agent.expertise,
        systemPrompt: agent.systemPrompt,
        avatar: agent.avatar
      })),
      results: config.includeResults ? data.results : undefined
    };

    const jsonString = JSON.stringify(promptxData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.promptx.mimeType });
  }

  /**
   * Export to standard JSON format
   */
  private exportToJSON(data: ExportData, config: ExportConfiguration): Blob {
    const jsonData = {
      ...data,
      exportConfiguration: config
    };

    const jsonString = JSON.stringify(jsonData, null, 2);
    return new Blob([jsonString], { type: EXPORT_FORMATS.json.mimeType });
  }

  /**
   * Export to CSV format
   */
  private exportToCSV(data: ExportData, config: ExportConfiguration): Blob {
    let csvContent = '';

    if (data.prompts && data.prompts.length > 0) {
      csvContent += 'Type,Name,Content,Purpose,Tags,Variables\n';
      
      data.prompts.forEach(prompt => {
        const row = [
          'Prompt',
          this.escapeCsvField(prompt.name),
          this.escapeCsvField(prompt.prompt),
          this.escapeCsvField(prompt.purpose || ''),
          this.escapeCsvField(prompt.tags?.join(';') || ''),
          this.escapeCsvField(JSON.stringify(prompt.variables))
        ].join(',');
        csvContent += row + '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      if (csvContent) csvContent += '\n';
      csvContent += 'Type,Name,Role,Personality,Expertise,System Prompt\n';
      
      data.agents.forEach(agent => {
        const row = [
          'Agent',
          this.escapeCsvField(agent.name),
          this.escapeCsvField(agent.role),
          this.escapeCsvField(agent.personality || ''),
          this.escapeCsvField(agent.expertise?.join(';') || ''),
          this.escapeCsvField(agent.systemPrompt)
        ].join(',');
        csvContent += row + '\n';
      });
    }

    return new Blob([csvContent], { type: EXPORT_FORMATS.csv.mimeType });
  }

  /**
   * Export to Markdown format
   */
  private exportToMarkdown(data: ExportData, config: ExportConfiguration): Blob {
    let markdown = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      markdown += `**Exported:** ${data.metadata.exportedAt.toLocaleString()}\n`;
      markdown += `**Version:** ${data.metadata.version}\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      markdown += '## Prompts\n\n';
      
      data.prompts.forEach((prompt, index) => {
        markdown += `### ${index + 1}. ${prompt.name}\n\n`;
        
        if (prompt.purpose) {
          markdown += `**Purpose:** ${prompt.purpose}\n\n`;
        }
        
        if (prompt.tags && prompt.tags.length > 0) {
          markdown += `**Tags:** ${prompt.tags.join(', ')}\n\n`;
        }

        if (prompt.systemPrompt) {
          markdown += '**System Prompt:**\n```\n';
          markdown += prompt.systemPrompt;
          markdown += '\n```\n\n';
        }

        markdown += '**Prompt:**\n```\n';
        markdown += prompt.prompt;
        markdown += '\n```\n\n';

        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          markdown += '**Variables:**\n';
          Object.entries(prompt.variables).forEach(([key, value]) => {
            markdown += `- **${key}:** ${value}\n`;
          });
          markdown += '\n';
        }

        markdown += '---\n\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      markdown += '## Agents\n\n';
      
      data.agents.forEach((agent, index) => {
        markdown += `### ${index + 1}. ${agent.name}\n\n`;
        markdown += `**Role:** ${agent.role}\n\n`;
        
        if (agent.personality) {
          markdown += `**Personality:** ${agent.personality}\n\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          markdown += `**Expertise:** ${agent.expertise.join(', ')}\n\n`;
        }

        markdown += '**System Prompt:**\n```\n';
        markdown += agent.systemPrompt;
        markdown += '\n```\n\n';

        markdown += '---\n\n';
      });
    }

    return new Blob([markdown], { type: EXPORT_FORMATS.markdown.mimeType });
  }

  /**
   * Export to YAML format
   */
  private exportToYAML(data: ExportData, config: ExportConfiguration): Blob {
    // Simple YAML serialization (for complex cases, would use a proper YAML library)
    let yaml = '# Prompt Studio Export\n\n';
    
    if (data.metadata) {
      yaml += 'metadata:\n';
      yaml += `  exportedAt: "${data.metadata.exportedAt.toISOString()}"\n`;
      yaml += `  version: "${data.metadata.version}"\n`;
      yaml += `  source: "${data.metadata.source}"\n\n`;
    }

    if (data.prompts && data.prompts.length > 0) {
      yaml += 'prompts:\n';
      data.prompts.forEach(prompt => {
        yaml += `  - id: "${prompt.id}"\n`;
        yaml += `    name: "${prompt.name}"\n`;
        yaml += `    prompt: |\n`;
        yaml += this.indentText(prompt.prompt, 6);
        
        if (prompt.systemPrompt) {
          yaml += `    systemPrompt: |\n`;
          yaml += this.indentText(prompt.systemPrompt, 6);
        }
        
        if (prompt.purpose) {
          yaml += `    purpose: "${prompt.purpose}"\n`;
        }
        
        if (prompt.tags && prompt.tags.length > 0) {
          yaml += `    tags:\n`;
          prompt.tags.forEach(tag => {
            yaml += `      - "${tag}"\n`;
          });
        }
        
        if (prompt.variables && Object.keys(prompt.variables).length > 0) {
          yaml += `    variables:\n`;
          Object.entries(prompt.variables).forEach(([key, value]) => {
            yaml += `      ${key}: "${value}"\n`;
          });
        }
        
        yaml += '\n';
      });
    }

    if (data.agents && data.agents.length > 0) {
      yaml += 'agents:\n';
      data.agents.forEach(agent => {
        yaml += `  - id: "${agent.id}"\n`;
        yaml += `    name: "${agent.name}"\n`;
        yaml += `    role: "${agent.role}"\n`;
        
        if (agent.personality) {
          yaml += `    personality: "${agent.personality}"\n`;
        }
        
        if (agent.expertise && agent.expertise.length > 0) {
          yaml += `    expertise:\n`;
          agent.expertise.forEach(skill => {
            yaml += `      - "${skill}"\n`;
          });
        }
        
        yaml += `    systemPrompt: |\n`;
        yaml += this.indentText(agent.systemPrompt, 6);
        yaml += '\n';
      });
    }

    return new Blob([yaml], { type: EXPORT_FORMATS.yaml.mimeType });
  }

  /**
   * Filter data based on export configuration
   */
  private filterData(data: ExportData, config: ExportConfiguration): ExportData {
    const filtered: ExportData = {};

    // Filter prompts
    if (data.prompts) {
      filtered.prompts = data.prompts.filter(prompt => {
        if (config.filters?.tags && config.filters.tags.length > 0) {
          const hasMatchingTag = prompt.tags?.some(tag => 
            config.filters!.tags.includes(tag)
          );
          if (!hasMatchingTag) return false;
        }
        return true;
      });
    }

    // Filter agents
    if (data.agents && config.includeAgents) {
      filtered.agents = data.agents;
    }

    // Filter results
    if (data.results && config.includeResults) {
      filtered.results = data.results.filter(result => {
        if (config.dateRange) {
          const resultDate = new Date(result.timestamp);
          if (resultDate < config.dateRange.start || resultDate > config.dateRange.end) {
            return false;
          }
        }

        if (config.filters?.types && config.filters.types.length > 0) {
          if (!config.filters.types.includes(result.type)) {
            return false;
          }
        }

        if (config.filters?.minScore && result.scores) {
          const avgScore = Object.values(result.scores).reduce((sum: number, score: any) => 
            sum + (typeof score === 'number' ? score : 0), 0
          ) / Object.keys(result.scores).length;
          
          if (avgScore < config.filters.minScore) {
            return false;
          }
        }

        return true;
      });
    }

    // Include metadata if requested
    if (config.includeMetadata && data.metadata) {
      filtered.metadata = data.metadata;
    }

    return filtered;
  }

  /**
   * Download exported data as file
   */
  downloadExport(blob: Blob, filename: string, format: ExportFormat): void {
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename.endsWith(format.extension) ? filename : `${filename}${format.extension}`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Generate filename based on export configuration
   */
  generateFilename(config: ExportConfiguration, data: ExportData): string {
    const timestamp = new Date().toISOString().split('T')[0];
    const format = EXPORT_FORMATS[config.format];
    
    let name = 'prompt-studio-export';
    
    if (data.prompts && data.prompts.length > 0) {
      name += `-${data.prompts.length}prompts`;
    }
    
    if (data.agents && data.agents.length > 0) {
      name += `-${data.agents.length}agents`;
    }
    
    if (data.results && data.results.length > 0) {
      name += `-${data.results.length}results`;
    }
    
    return `${name}-${timestamp}${format.extension}`;
  }

  /**
   * Utility methods
   */
  private escapeCsvField(field: string): string {
    if (field.includes(',') || field.includes('"') || field.includes('\n')) {
      return `"${field.replace(/"/g, '""')}"`;
    }
    return field;
  }

  private indentText(text: string, spaces: number): string {
    const indent = ' '.repeat(spaces);
    return text.split('\n').map(line => indent + line).join('\n') + '\n';
  }

  /**
   * Validate export configuration
   */
  validateConfiguration(config: ExportConfiguration): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!EXPORT_FORMATS[config.format]) {
      errors.push(`Unsupported export format: ${config.format}`);
    }

    if (config.dateRange) {
      if (config.dateRange.start > config.dateRange.end) {
        errors.push('Start date must be before end date');
      }
    }

    if (config.filters?.minScore !== undefined) {
      if (config.filters.minScore < 0 || config.filters.minScore > 100) {
        errors.push('Minimum score must be between 0 and 100');
      }
    }

    return {
      valid: errors.length === 0,
      errors
    };
  }

  /**
   * Get available export formats
   */
  getAvailableFormats(): ExportFormat[] {
    return Object.values(EXPORT_FORMATS);
  }

  /**
   * Integration with external services (Catalyst, Notion)
   */
  async exportToCatalyst(data: ExportData, config: any): Promise<{ success: boolean; message: string }> {
    // This would integrate with Catalyst API
    // For now, return a mock response
    return {
      success: true,
      message: 'Successfully exported to Catalyst vault'
    };
  }

  async exportToNotion(data: ExportData, config: any): Promise<{ success: boolean; message: string }> {
    // This would integrate with Notion API
    // For now, return a mock response
    return {
      success: true,
      message: 'Successfully exported to Notion page'
    };
  }
}

export const exportToolsService = new ExportToolsService();
