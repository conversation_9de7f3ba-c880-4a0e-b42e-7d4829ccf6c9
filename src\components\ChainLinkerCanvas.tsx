import { useState, useRef, useCallback, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Play, 
  Save, 
  Plus, 
  Trash2, 
  Settings, 
  Link, 
  Unlink,
  Download,
  Upload,
  AlertCircle,
  CheckCircle,
  Zap,
  Brain,
  Filter,
  FileOutput,
  FileInput
} from 'lucide-react';
import { 
  ChainFlow, 
  ChainNode, 
  ChainConnection, 
  chainLinkerService 
} from '@/lib/chain-linker';
import { toast } from '@/hooks/use-toast';

interface CanvasPosition {
  x: number;
  y: number;
}

interface DragState {
  isDragging: boolean;
  dragType: 'node' | 'connection' | 'canvas';
  dragData?: any;
  startPosition?: CanvasPosition;
  currentPosition?: CanvasPosition;
}

export const ChainLinkerCanvas = () => {
  const [flow, setFlow] = useState<ChainFlow>(
    chainLinkerService.createFlow('New Flow', 'Drag and drop prompt flow')
  );
  const [selectedNode, setSelectedNode] = useState<ChainNode | null>(null);
  const [dragState, setDragState] = useState<DragState>({ isDragging: false, dragType: 'canvas' });
  const [canvasOffset, setCanvasOffset] = useState<CanvasPosition>({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [isExecuting, setIsExecuting] = useState(false);
  const [executionResult, setExecutionResult] = useState<any>(null);
  
  const canvasRef = useRef<HTMLDivElement>(null);
  const svgRef = useRef<SVGSVGElement>(null);

  const nodeTypes = [
    { type: 'input', icon: FileInput, label: 'Input', color: 'bg-green-600' },
    { type: 'prompt', icon: Zap, label: 'Prompt', color: 'bg-blue-600' },
    { type: 'agent', icon: Brain, label: 'Agent', color: 'bg-purple-600' },
    { type: 'condition', icon: Filter, label: 'Condition', color: 'bg-yellow-600' },
    { type: 'output', icon: FileOutput, label: 'Output', color: 'bg-red-600' }
  ];

  const handleCanvasMouseDown = useCallback((e: React.MouseEvent) => {
    if (e.target === canvasRef.current) {
      setDragState({
        isDragging: true,
        dragType: 'canvas',
        startPosition: { x: e.clientX - canvasOffset.x, y: e.clientY - canvasOffset.y }
      });
    }
  }, [canvasOffset]);

  const handleCanvasMouseMove = useCallback((e: React.MouseEvent) => {
    if (dragState.isDragging && dragState.dragType === 'canvas' && dragState.startPosition) {
      setCanvasOffset({
        x: e.clientX - dragState.startPosition.x,
        y: e.clientY - dragState.startPosition.y
      });
    }
  }, [dragState]);

  const handleCanvasMouseUp = useCallback(() => {
    setDragState({ isDragging: false, dragType: 'canvas' });
  }, []);

  const addNode = (nodeType: ChainNode['type'], position?: CanvasPosition) => {
    const canvasRect = canvasRef.current?.getBoundingClientRect();
    const defaultPosition = canvasRect ? {
      x: (canvasRect.width / 2 - canvasOffset.x) / zoom,
      y: (canvasRect.height / 2 - canvasOffset.y) / zoom
    } : { x: 200, y: 200 };

    const newNode = chainLinkerService.addNode(flow, nodeType, position || defaultPosition);
    setFlow({ ...flow });
    setSelectedNode(newNode);
    
    toast({
      title: "Node Added",
      description: `Added ${nodeType} node to the flow`,
    });
  };

  const removeNode = (nodeId: string) => {
    chainLinkerService.removeNode(flow, nodeId);
    setFlow({ ...flow });
    if (selectedNode?.id === nodeId) {
      setSelectedNode(null);
    }
    
    toast({
      title: "Node Removed",
      description: "Node and its connections have been removed",
    });
  };

  const updateNodeData = (nodeId: string, data: Partial<ChainNode['data']>) => {
    const node = flow.nodes.find(n => n.id === nodeId);
    if (node) {
      node.data = { ...node.data, ...data };
      setFlow({ ...flow });
    }
  };

  const executeFlow = async () => {
    setIsExecuting(true);
    setExecutionResult(null);

    try {
      const validation = chainLinkerService.validateFlow(flow);
      if (!validation.valid) {
        toast({
          title: "Validation Failed",
          description: validation.errors.join(', '),
          variant: "destructive"
        });
        return;
      }

      const result = await chainLinkerService.executeFlow(flow, {});
      setExecutionResult(result);
      
      if (result.success) {
        toast({
          title: "Execution Complete",
          description: `Flow executed successfully in ${result.duration}ms`,
        });
      } else {
        toast({
          title: "Execution Failed",
          description: result.errors?.[0]?.message || "Unknown error",
          variant: "destructive"
        });
      }
    } catch (error) {
      toast({
        title: "Execution Error",
        description: error instanceof Error ? error.message : "Failed to execute flow",
        variant: "destructive"
      });
    } finally {
      setIsExecuting(false);
    }
  };

  const saveFlow = () => {
    // In a real app, this would save to a backend
    const flowData = JSON.stringify(flow, null, 2);
    const blob = new Blob([flowData], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${flow.name.replace(/\s+/g, '_')}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    toast({
      title: "Flow Saved",
      description: "Flow has been downloaded as JSON",
    });
  };

  const getNodeIcon = (nodeType: string) => {
    const nodeTypeConfig = nodeTypes.find(nt => nt.type === nodeType);
    return nodeTypeConfig?.icon || Zap;
  };

  const getNodeColor = (nodeType: string) => {
    const nodeTypeConfig = nodeTypes.find(nt => nt.type === nodeType);
    return nodeTypeConfig?.color || 'bg-gray-600';
  };

  return (
    <div className="h-full flex flex-col">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-slate-700">
        <div className="flex items-center gap-4">
          <h2 className="text-xl font-bold text-slate-200">Chain Linker Canvas</h2>
          <Badge variant="outline" className="border-slate-500 text-slate-300">
            {flow.nodes.length} nodes, {flow.connections.length} connections
          </Badge>
        </div>
        
        <div className="flex items-center gap-2">
          <Button onClick={executeFlow} disabled={isExecuting} className="bg-green-600 hover:bg-green-700">
            {isExecuting ? (
              <>
                <Zap className="w-4 h-4 mr-2 animate-pulse" />
                Executing...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Execute
              </>
            )}
          </Button>
          <Button onClick={saveFlow} variant="outline">
            <Save className="w-4 h-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Node Palette */}
        <div className="w-64 border-r border-slate-700 p-4 space-y-4">
          <h3 className="font-semibold text-slate-200">Node Types</h3>
          <div className="space-y-2">
            {nodeTypes.map(({ type, icon: Icon, label, color }) => (
              <Button
                key={type}
                onClick={() => addNode(type as ChainNode['type'])}
                variant="outline"
                className="w-full justify-start border-slate-600 hover:bg-slate-700"
              >
                <div className={`w-3 h-3 rounded-full ${color} mr-2`} />
                <Icon className="w-4 h-4 mr-2" />
                {label}
              </Button>
            ))}
          </div>

          {/* Flow Info */}
          <div className="space-y-2 pt-4 border-t border-slate-700">
            <Label htmlFor="flow-name" className="text-slate-200">Flow Name</Label>
            <Input
              id="flow-name"
              value={flow.name}
              onChange={(e) => setFlow({ ...flow, name: e.target.value })}
              className="bg-slate-900/50 border-slate-600 text-white"
            />
            
            <Label htmlFor="flow-description" className="text-slate-200">Description</Label>
            <Textarea
              id="flow-description"
              value={flow.description}
              onChange={(e) => setFlow({ ...flow, description: e.target.value })}
              className="bg-slate-900/50 border-slate-600 text-white min-h-[60px]"
            />
          </div>
        </div>

        {/* Canvas */}
        <div className="flex-1 relative overflow-hidden bg-slate-900">
          <div
            ref={canvasRef}
            className="w-full h-full relative cursor-move"
            onMouseDown={handleCanvasMouseDown}
            onMouseMove={handleCanvasMouseMove}
            onMouseUp={handleCanvasMouseUp}
            style={{
              backgroundImage: `radial-gradient(circle, #374151 1px, transparent 1px)`,
              backgroundSize: `${20 * zoom}px ${20 * zoom}px`,
              backgroundPosition: `${canvasOffset.x}px ${canvasOffset.y}px`
            }}
          >
            {/* Nodes */}
            {flow.nodes.map(node => {
              const Icon = getNodeIcon(node.type);
              const isSelected = selectedNode?.id === node.id;
              
              return (
                <div
                  key={node.id}
                  className={`absolute cursor-pointer transform transition-transform hover:scale-105 ${
                    isSelected ? 'ring-2 ring-blue-400' : ''
                  }`}
                  style={{
                    left: node.position.x * zoom + canvasOffset.x,
                    top: node.position.y * zoom + canvasOffset.y,
                    transform: `scale(${zoom})`
                  }}
                  onClick={() => setSelectedNode(node)}
                >
                  <Card className="w-48 bg-slate-800 border-slate-600">
                    <CardHeader className="pb-2">
                      <CardTitle className="text-sm flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${getNodeColor(node.type)}`} />
                        <Icon className="w-4 h-4" />
                        {node.data.title}
                        <Button
                          onClick={(e) => {
                            e.stopPropagation();
                            removeNode(node.id);
                          }}
                          size="sm"
                          variant="ghost"
                          className="ml-auto h-6 w-6 p-0 text-red-400 hover:text-red-300"
                        >
                          <Trash2 className="w-3 h-3" />
                        </Button>
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="pt-0">
                      <p className="text-xs text-slate-400 mb-2">
                        {node.data.description}
                      </p>
                      
                      {/* Input/Output ports */}
                      <div className="flex justify-between text-xs">
                        <div className="space-y-1">
                          {node.inputs.map(input => (
                            <div key={input.id} className="flex items-center gap-1">
                              <div className="w-2 h-2 rounded-full bg-blue-400" />
                              <span className="text-slate-300">{input.label}</span>
                            </div>
                          ))}
                        </div>
                        <div className="space-y-1 text-right">
                          {node.outputs.map(output => (
                            <div key={output.id} className="flex items-center gap-1 justify-end">
                              <span className="text-slate-300">{output.label}</span>
                              <div className="w-2 h-2 rounded-full bg-green-400" />
                            </div>
                          ))}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              );
            })}

            {/* SVG for connections */}
            <svg
              ref={svgRef}
              className="absolute inset-0 pointer-events-none"
              style={{ width: '100%', height: '100%' }}
            >
              {flow.connections.map(connection => {
                const sourceNode = flow.nodes.find(n => n.id === connection.sourceNodeId);
                const targetNode = flow.nodes.find(n => n.id === connection.targetNodeId);
                
                if (!sourceNode || !targetNode) return null;
                
                const sourceX = (sourceNode.position.x + 192) * zoom + canvasOffset.x; // 192 = node width
                const sourceY = (sourceNode.position.y + 40) * zoom + canvasOffset.y;
                const targetX = targetNode.position.x * zoom + canvasOffset.x;
                const targetY = (targetNode.position.y + 40) * zoom + canvasOffset.y;
                
                return (
                  <line
                    key={connection.id}
                    x1={sourceX}
                    y1={sourceY}
                    x2={targetX}
                    y2={targetY}
                    stroke="#60a5fa"
                    strokeWidth="2"
                    markerEnd="url(#arrowhead)"
                  />
                );
              })}
              
              {/* Arrow marker definition */}
              <defs>
                <marker
                  id="arrowhead"
                  markerWidth="10"
                  markerHeight="7"
                  refX="9"
                  refY="3.5"
                  orient="auto"
                >
                  <polygon
                    points="0 0, 10 3.5, 0 7"
                    fill="#60a5fa"
                  />
                </marker>
              </defs>
            </svg>
          </div>

          {/* Zoom Controls */}
          <div className="absolute bottom-4 right-4 flex gap-2">
            <Button
              onClick={() => setZoom(Math.max(0.5, zoom - 0.1))}
              size="sm"
              variant="outline"
            >
              -
            </Button>
            <span className="px-2 py-1 bg-slate-800 rounded text-sm text-slate-200">
              {Math.round(zoom * 100)}%
            </span>
            <Button
              onClick={() => setZoom(Math.min(2, zoom + 0.1))}
              size="sm"
              variant="outline"
            >
              +
            </Button>
          </div>
        </div>

        {/* Properties Panel */}
        {selectedNode && (
          <div className="w-80 border-l border-slate-700 p-4 space-y-4">
            <div className="flex items-center justify-between">
              <h3 className="font-semibold text-slate-200">Node Properties</h3>
              <Button
                onClick={() => setSelectedNode(null)}
                size="sm"
                variant="ghost"
              >
                ×
              </Button>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="node-title" className="text-slate-200">Title</Label>
                <Input
                  id="node-title"
                  value={selectedNode.data.title}
                  onChange={(e) => updateNodeData(selectedNode.id, { title: e.target.value })}
                  className="bg-slate-900/50 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label htmlFor="node-description" className="text-slate-200">Description</Label>
                <Textarea
                  id="node-description"
                  value={selectedNode.data.description || ''}
                  onChange={(e) => updateNodeData(selectedNode.id, { description: e.target.value })}
                  className="bg-slate-900/50 border-slate-600 text-white min-h-[60px]"
                />
              </div>

              {selectedNode.type === 'prompt' && (
                <div>
                  <Label htmlFor="node-content" className="text-slate-200">Prompt Content</Label>
                  <Textarea
                    id="node-content"
                    value={selectedNode.data.content || ''}
                    onChange={(e) => updateNodeData(selectedNode.id, { content: e.target.value })}
                    className="bg-slate-900/50 border-slate-600 text-white min-h-[100px]"
                    placeholder="Enter your prompt here..."
                  />
                </div>
              )}

              {selectedNode.type === 'condition' && (
                <div>
                  <Label htmlFor="node-condition" className="text-slate-200">Condition</Label>
                  <Textarea
                    id="node-condition"
                    value={selectedNode.data.content || ''}
                    onChange={(e) => updateNodeData(selectedNode.id, { content: e.target.value })}
                    className="bg-slate-900/50 border-slate-600 text-white min-h-[60px]"
                    placeholder="input.length > 0"
                  />
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Execution Results */}
      {executionResult && (
        <div className="border-t border-slate-700 p-4">
          <div className="flex items-center gap-2 mb-2">
            {executionResult.success ? (
              <CheckCircle className="w-5 h-5 text-green-400" />
            ) : (
              <AlertCircle className="w-5 h-5 text-red-400" />
            )}
            <h3 className="font-semibold text-slate-200">Execution Results</h3>
            <Badge variant={executionResult.success ? "default" : "destructive"}>
              {executionResult.success ? 'Success' : 'Failed'}
            </Badge>
          </div>
          
          <div className="bg-slate-900/50 p-3 rounded text-sm text-slate-300">
            <pre>{JSON.stringify(executionResult, null, 2)}</pre>
          </div>
        </div>
      )}
    </div>
  );
};
