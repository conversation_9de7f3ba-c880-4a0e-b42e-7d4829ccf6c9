import { aiService, AIResponse, AI_PROVIDERS } from './ai-service';

export interface PromptTestRequest {
  prompt: string;
  systemPrompt?: string;
  models: string[];
  temperature?: number;
  maxTokens?: number;
  variables?: Record<string, string>;
}

export interface ModelTestResult {
  model: string;
  provider: string;
  response: string;
  timeToCompletion: number;
  tokens: {
    prompt: number;
    completion: number;
    total: number;
  };
  score: {
    fidelity: number;
    adherence: number;
    consistency: number;
    creativity: number;
  };
  aiResponse: AIResponse;
  error?: string;
}

export interface PromptTestResult {
  id: string;
  timestamp: Date;
  prompt: string;
  systemPrompt?: string;
  variables?: Record<string, string>;
  results: ModelTestResult[];
  summary: {
    averageScore: number;
    bestModel: string;
    worstModel: string;
    totalTokens: number;
    averageTime: number;
  };
  metadata: {
    totalModels: number;
    successfulTests: number;
    failedTests: number;
    duration: number;
  };
}

class PromptTestingService {
  async runPromptTest(request: PromptTestRequest): Promise<PromptTestResult> {
    const startTime = Date.now();
    const results: ModelTestResult[] = [];

    // Process variables in prompt
    const processedPrompt = this.processVariables(request.prompt, request.variables);
    const processedSystemPrompt = request.systemPrompt ? 
      this.processVariables(request.systemPrompt, request.variables) : undefined;

    // Test each model
    for (const modelSpec of request.models) {
      try {
        const result = await this.testModel(
          modelSpec,
          processedPrompt,
          processedSystemPrompt,
          request.temperature,
          request.maxTokens
        );
        results.push(result);
      } catch (error) {
        console.error(`Failed to test model ${modelSpec}:`, error);
        results.push(this.createFailedResult(modelSpec, error as Error));
      }
    }

    const duration = Date.now() - startTime;
    const summary = this.generateSummary(results);

    return {
      id: Date.now().toString(),
      timestamp: new Date(),
      prompt: processedPrompt,
      systemPrompt: processedSystemPrompt,
      variables: request.variables,
      results,
      summary,
      metadata: {
        totalModels: request.models.length,
        successfulTests: results.filter(r => !r.error).length,
        failedTests: results.filter(r => r.error).length,
        duration
      }
    };
  }

  private async testModel(
    modelSpec: string,
    prompt: string,
    systemPrompt?: string,
    temperature?: number,
    maxTokens?: number
  ): Promise<ModelTestResult> {
    const startTime = Date.now();
    
    // Parse model specification (format: "provider:model" or just "model")
    const { provider, model } = this.parseModelSpec(modelSpec);

    // Build messages
    const messages: Array<{ role: 'system' | 'user' | 'assistant'; content: string }> = [];
    
    if (systemPrompt) {
      messages.push({ role: 'system', content: systemPrompt });
    }
    
    messages.push({ role: 'user', content: prompt });

    // Make AI request
    const aiResponse = await aiService.generateResponse({
      provider,
      model,
      messages,
      temperature: temperature || 0.7,
      maxTokens: maxTokens || 1024
    });

    const timeToCompletion = Date.now() - startTime;

    // Calculate scores
    const scores = this.calculateScores(aiResponse.content, prompt, systemPrompt);

    return {
      model: aiResponse.model,
      provider: aiResponse.provider,
      response: aiResponse.content,
      timeToCompletion,
      tokens: {
        prompt: aiResponse.usage?.promptTokens || 0,
        completion: aiResponse.usage?.completionTokens || 0,
        total: aiResponse.usage?.totalTokens || 0
      },
      score: scores,
      aiResponse
    };
  }

  private parseModelSpec(modelSpec: string): { provider: string; model: string } {
    // Handle different model specification formats
    if (modelSpec.includes(':')) {
      const [provider, model] = modelSpec.split(':');
      return { provider, model };
    }

    // Map common model names to providers
    const modelMappings: Record<string, { provider: string; model: string }> = {
      'openai': { provider: 'openai', model: 'gpt-4' },
      'gpt-4': { provider: 'openai', model: 'gpt-4' },
      'gpt-4-turbo': { provider: 'openai', model: 'gpt-4-turbo' },
      'gpt-3.5-turbo': { provider: 'openai', model: 'gpt-3.5-turbo' },
      'anthropic': { provider: 'anthropic', model: 'claude-3-sonnet' },
      'claude-3-opus': { provider: 'anthropic', model: 'claude-3-opus' },
      'claude-3-sonnet': { provider: 'anthropic', model: 'claude-3-sonnet' },
      'claude-3-haiku': { provider: 'anthropic', model: 'claude-3-haiku' }
    };

    const mapping = modelMappings[modelSpec.toLowerCase()];
    if (mapping) {
      return mapping;
    }

    // Default to OpenAI if no mapping found
    return { provider: 'openai', model: modelSpec };
  }

  private processVariables(text: string, variables?: Record<string, string>): string {
    if (!variables) return text;

    let processedText = text;
    for (const [key, value] of Object.entries(variables)) {
      // Replace {{variable}} and {variable} patterns
      const patterns = [
        new RegExp(`\\{\\{${key}\\}\\}`, 'g'),
        new RegExp(`\\{${key}\\}`, 'g')
      ];
      
      for (const pattern of patterns) {
        processedText = processedText.replace(pattern, value);
      }
    }

    return processedText;
  }

  private calculateScores(response: string, prompt: string, systemPrompt?: string): {
    fidelity: number;
    adherence: number;
    consistency: number;
    creativity: number;
  } {
    // Fidelity: How well the response addresses the prompt
    const fidelity = this.calculateFidelity(response, prompt);
    
    // Adherence: How well the response follows system instructions
    const adherence = this.calculateAdherence(response, systemPrompt);
    
    // Consistency: Internal consistency of the response
    const consistency = this.calculateConsistency(response);
    
    // Creativity: Originality and creativity of the response
    const creativity = this.calculateCreativity(response);

    return { fidelity, adherence, consistency, creativity };
  }

  private calculateFidelity(response: string, prompt: string): number {
    let score = 50; // Base score

    // Length appropriateness (not too short, not excessively long)
    if (response.length > 50 && response.length < 2000) score += 15;
    else if (response.length > 20) score += 10;

    // Relevance indicators
    const promptWords = prompt.toLowerCase().split(/\s+/).filter(w => w.length > 3);
    const responseWords = response.toLowerCase().split(/\s+/);
    const relevantWords = promptWords.filter(word => responseWords.includes(word));
    score += Math.min(20, (relevantWords.length / promptWords.length) * 20);

    // Structure and completeness
    if (response.includes('\n') || response.match(/\d+\./)) score += 10;
    if (response.endsWith('.') || response.endsWith('!') || response.endsWith('?')) score += 5;

    return Math.min(100, Math.max(0, score));
  }

  private calculateAdherence(response: string, systemPrompt?: string): number {
    if (!systemPrompt) return 75; // Default score when no system prompt

    let score = 50;

    // Check for format adherence
    if (systemPrompt.toLowerCase().includes('format') || systemPrompt.toLowerCase().includes('structure')) {
      if (response.includes('\n') || response.match(/\d+\./) || response.includes('•')) {
        score += 20;
      }
    }

    // Check for tone adherence
    const toneWords = ['professional', 'casual', 'formal', 'friendly', 'technical'];
    const systemTone = toneWords.find(tone => systemPrompt.toLowerCase().includes(tone));
    if (systemTone) {
      // Simple heuristic for tone matching
      const responseTone = this.detectTone(response);
      if (responseTone === systemTone) score += 15;
    }

    // Check for instruction following
    if (systemPrompt.toLowerCase().includes('list') && response.match(/\d+\.|•|\-/)) score += 15;
    if (systemPrompt.toLowerCase().includes('explain') && response.length > 100) score += 10;

    return Math.min(100, Math.max(0, score));
  }

  private calculateConsistency(response: string): number {
    let score = 60; // Base score

    // Check for contradictions (simple heuristic)
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 10);
    if (sentences.length > 1) {
      // Look for contradictory words
      const contradictoryPairs = [
        ['yes', 'no'], ['good', 'bad'], ['always', 'never'],
        ['increase', 'decrease'], ['positive', 'negative']
      ];

      let contradictions = 0;
      for (const [word1, word2] of contradictoryPairs) {
        if (response.toLowerCase().includes(word1) && response.toLowerCase().includes(word2)) {
          contradictions++;
        }
      }

      score -= contradictions * 10;
    }

    // Coherence indicators
    const coherenceWords = ['therefore', 'however', 'furthermore', 'additionally', 'consequently'];
    const coherenceCount = coherenceWords.filter(word => 
      response.toLowerCase().includes(word)
    ).length;
    score += Math.min(15, coherenceCount * 5);

    return Math.min(100, Math.max(0, score));
  }

  private calculateCreativity(response: string): number {
    let score = 50; // Base score

    // Vocabulary diversity
    const words = response.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const diversity = uniqueWords.size / words.length;
    score += diversity * 20;

    // Use of examples or analogies
    if (response.toLowerCase().includes('example') || response.toLowerCase().includes('like')) {
      score += 10;
    }

    // Creative language patterns
    const creativePatterns = [
      /metaphor|analogy|imagine|picture|envision/i,
      /innovative|creative|unique|novel|original/i
    ];

    for (const pattern of creativePatterns) {
      if (pattern.test(response)) score += 5;
    }

    return Math.min(100, Math.max(0, score));
  }

  private detectTone(response: string): string {
    const lowerResponse = response.toLowerCase();
    
    if (lowerResponse.includes('please') || lowerResponse.includes('thank you')) {
      return 'professional';
    } else if (lowerResponse.includes('hey') || lowerResponse.includes('awesome')) {
      return 'casual';
    } else if (lowerResponse.includes('furthermore') || lowerResponse.includes('therefore')) {
      return 'formal';
    } else if (lowerResponse.includes('!') || lowerResponse.includes('great')) {
      return 'friendly';
    } else {
      return 'technical';
    }
  }

  private createFailedResult(modelSpec: string, error: Error): ModelTestResult {
    const { provider, model } = this.parseModelSpec(modelSpec);
    
    return {
      model,
      provider,
      response: '',
      timeToCompletion: 0,
      tokens: { prompt: 0, completion: 0, total: 0 },
      score: { fidelity: 0, adherence: 0, consistency: 0, creativity: 0 },
      aiResponse: {
        content: '',
        model,
        provider,
        timestamp: new Date()
      },
      error: error.message
    };
  }

  private generateSummary(results: ModelTestResult[]): {
    averageScore: number;
    bestModel: string;
    worstModel: string;
    totalTokens: number;
    averageTime: number;
  } {
    const successfulResults = results.filter(r => !r.error);
    
    if (successfulResults.length === 0) {
      return {
        averageScore: 0,
        bestModel: 'None',
        worstModel: 'None',
        totalTokens: 0,
        averageTime: 0
      };
    }

    // Calculate average scores
    const averageScores = successfulResults.map(r => 
      (r.score.fidelity + r.score.adherence + r.score.consistency + r.score.creativity) / 4
    );
    
    const averageScore = averageScores.reduce((sum, score) => sum + score, 0) / averageScores.length;
    
    // Find best and worst models
    const bestIndex = averageScores.indexOf(Math.max(...averageScores));
    const worstIndex = averageScores.indexOf(Math.min(...averageScores));
    
    const bestModel = successfulResults[bestIndex]?.model || 'Unknown';
    const worstModel = successfulResults[worstIndex]?.model || 'Unknown';
    
    // Calculate totals
    const totalTokens = results.reduce((sum, r) => sum + r.tokens.total, 0);
    const averageTime = results.reduce((sum, r) => sum + r.timeToCompletion, 0) / results.length;

    return {
      averageScore,
      bestModel,
      worstModel,
      totalTokens,
      averageTime
    };
  }

  // Utility method to get available models
  getAvailableModels(): Array<{ id: string; name: string; provider: string }> {
    const models: Array<{ id: string; name: string; provider: string }> = [];
    
    for (const [providerId, provider] of Object.entries(AI_PROVIDERS)) {
      for (const model of provider.models) {
        models.push({
          id: `${providerId}:${model}`,
          name: model,
          provider: provider.name
        });
      }
    }

    return models;
  }
}

export const promptTestingService = new PromptTestingService();
