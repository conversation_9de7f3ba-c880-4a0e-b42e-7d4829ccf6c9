import { aiService } from './ai-service';
import { agentSimulationService } from './agent-simulation';
import { Agent, PromptVariant } from '@/store/promptStore';

export interface ProjectStep {
  id: string;
  name: string;
  description: string;
  estimatedTime: number;
  dependencies: string[];
  completed: boolean;
  status: 'pending' | 'in_progress' | 'completed' | 'failed';
  result?: string;
  issues?: string[];
  recommendations?: string[];
}

export interface ProjectSimulationRequest {
  name: string;
  description: string;
  projectType: string;
  complexity: 'low' | 'medium' | 'high';
  agents: Agent[];
  prompts: PromptVariant[];
  scenario?: string;
  customSteps?: Partial<ProjectStep>[];
}

export interface ProjectSimulationResult {
  id: string;
  timestamp: Date;
  name: string;
  description: string;
  projectType: string;
  complexity: 'low' | 'medium' | 'high';
  steps: ProjectStep[];
  totalEstimatedTime: number;
  actualTime: number;
  successRate: number;
  overallQuality: number;
  agentContributions: Array<{
    agentId: string;
    agentName: string;
    stepsInvolved: string[];
    contribution: string;
    effectiveness: number;
  }>;
  promptUsage: Array<{
    promptId: string;
    promptName: string;
    stepsUsed: string[];
    effectiveness: number;
  }>;
  insights: {
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    riskFactors: string[];
  };
  metadata: {
    totalTokens: number;
    duration: number;
    modelsUsed: string[];
  };
}

class ProjectSimulationService {
  async runProjectSimulation(request: ProjectSimulationRequest): Promise<ProjectSimulationResult> {
    const startTime = Date.now();
    let totalTokens = 0;

    // Generate project steps if not provided
    const steps = request.customSteps?.length 
      ? await this.processCustomSteps(request.customSteps, request)
      : await this.generateProjectSteps(request);

    // Simulate each step with AI agents and prompts
    const simulatedSteps: ProjectStep[] = [];
    let actualTime = 0;

    for (const step of steps) {
      const stepResult = await this.simulateProjectStep(step, request);
      simulatedSteps.push(stepResult.step);
      actualTime += stepResult.actualTime;
      totalTokens += stepResult.tokensUsed;
    }

    // Analyze agent contributions
    const agentContributions = await this.analyzeAgentContributions(request.agents, simulatedSteps);
    
    // Analyze prompt usage
    const promptUsage = await this.analyzePromptUsage(request.prompts, simulatedSteps);

    // Generate insights
    const insights = await this.generateProjectInsights(simulatedSteps, request);

    // Calculate metrics
    const successRate = (simulatedSteps.filter(s => s.status === 'completed').length / simulatedSteps.length) * 100;
    const overallQuality = this.calculateOverallQuality(simulatedSteps);

    const duration = Date.now() - startTime;

    return {
      id: Date.now().toString(),
      timestamp: new Date(),
      name: request.name,
      description: request.description,
      projectType: request.projectType,
      complexity: request.complexity,
      steps: simulatedSteps,
      totalEstimatedTime: steps.reduce((sum, step) => sum + step.estimatedTime, 0),
      actualTime,
      successRate,
      overallQuality,
      agentContributions,
      promptUsage,
      insights,
      metadata: {
        totalTokens,
        duration,
        modelsUsed: ['gpt-4', 'claude-3-sonnet'] // Default models used
      }
    };
  }

  private async generateProjectSteps(request: ProjectSimulationRequest): Promise<ProjectStep[]> {
    const prompt = `Generate a detailed project breakdown for the following project:

Project: ${request.name}
Description: ${request.description}
Type: ${request.projectType}
Complexity: ${request.complexity}

Please provide 5-8 project steps with the following format for each step:
- Step Name
- Description (1-2 sentences)
- Estimated Time (in hours)
- Dependencies (list of step names this depends on)

Consider the complexity level and project type when estimating times and dependencies.`;

    try {
      const response = await aiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are a project management expert. Generate realistic project breakdowns with accurate time estimates and dependencies.' },
          { role: 'user', content: prompt }
        ],
        temperature: 0.3,
        maxTokens: 1024
      });

      return this.parseProjectSteps(response.content);
    } catch (error) {
      console.error('Failed to generate project steps:', error);
      return this.getDefaultSteps(request.complexity);
    }
  }

  private parseProjectSteps(content: string): ProjectStep[] {
    const steps: ProjectStep[] = [];
    const lines = content.split('\n').filter(line => line.trim());
    
    let currentStep: Partial<ProjectStep> = {};
    let stepCounter = 1;

    for (const line of lines) {
      const trimmed = line.trim();
      
      if (trimmed.includes('Step') || trimmed.match(/^\d+\./)) {
        if (currentStep.name) {
          steps.push(this.completeStep(currentStep, stepCounter++));
          currentStep = {};
        }
        currentStep.name = trimmed.replace(/^\d+\.\s*/, '').replace(/Step\s*\d*:?\s*/i, '');
      } else if (trimmed.toLowerCase().includes('description')) {
        currentStep.description = trimmed.replace(/description:?\s*/i, '');
      } else if (trimmed.toLowerCase().includes('time') || trimmed.toLowerCase().includes('hours')) {
        const timeMatch = trimmed.match(/(\d+)\s*hours?/i);
        if (timeMatch) {
          currentStep.estimatedTime = parseInt(timeMatch[1]);
        }
      } else if (trimmed.toLowerCase().includes('dependencies')) {
        // Parse dependencies - this is simplified
        currentStep.dependencies = [];
      }
    }

    if (currentStep.name) {
      steps.push(this.completeStep(currentStep, stepCounter));
    }

    return steps.length > 0 ? steps : this.getDefaultSteps('medium');
  }

  private completeStep(partial: Partial<ProjectStep>, index: number): ProjectStep {
    return {
      id: `step_${index}`,
      name: partial.name || `Step ${index}`,
      description: partial.description || 'Project step description',
      estimatedTime: partial.estimatedTime || 24,
      dependencies: partial.dependencies || [],
      completed: false,
      status: 'pending'
    };
  }

  private getDefaultSteps(complexity: string): ProjectStep[] {
    const baseSteps = [
      { name: 'Project Planning', description: 'Define requirements and create project plan', estimatedTime: 16 },
      { name: 'Architecture Design', description: 'Design system architecture and components', estimatedTime: 24 },
      { name: 'Core Development', description: 'Implement core functionality', estimatedTime: 48 },
      { name: 'Testing & QA', description: 'Test functionality and fix issues', estimatedTime: 32 },
      { name: 'Deployment', description: 'Deploy to production environment', estimatedTime: 16 }
    ];

    const multiplier = complexity === 'high' ? 1.5 : complexity === 'low' ? 0.7 : 1;

    return baseSteps.map((step, index) => ({
      id: `step_${index + 1}`,
      name: step.name,
      description: step.description,
      estimatedTime: Math.round(step.estimatedTime * multiplier),
      dependencies: index > 0 ? [`step_${index}`] : [],
      completed: false,
      status: 'pending' as const
    }));
  }

  private async processCustomSteps(customSteps: Partial<ProjectStep>[], request: ProjectSimulationRequest): Promise<ProjectStep[]> {
    return customSteps.map((step, index) => ({
      id: step.id || `custom_step_${index + 1}`,
      name: step.name || `Custom Step ${index + 1}`,
      description: step.description || 'Custom project step',
      estimatedTime: step.estimatedTime || 24,
      dependencies: step.dependencies || [],
      completed: false,
      status: 'pending' as const
    }));
  }

  private async simulateProjectStep(
    step: ProjectStep, 
    request: ProjectSimulationRequest
  ): Promise<{ step: ProjectStep; actualTime: number; tokensUsed: number }> {
    const startTime = Date.now();
    let tokensUsed = 0;

    try {
      // Simulate step execution with agents
      if (request.agents.length > 0) {
        const stepScenario = `Execute project step: ${step.name}\nDescription: ${step.description}\nProject Context: ${request.description}`;
        
        const simulation = await agentSimulationService.runSimulation({
          agents: request.agents.slice(0, 2), // Use first 2 agents for efficiency
          scenario: stepScenario,
          temperature: 0.7,
          maxTokens: 512
        });

        tokensUsed += simulation.metadata.totalTokens;

        // Determine step outcome based on agent responses
        const avgConfidence = simulation.metadata.averageConfidence;
        const hasIssues = simulation.agentResponses.some(r => r.sentiment === 'critical');

        step.status = avgConfidence > 70 ? 'completed' : hasIssues ? 'failed' : 'completed';
        step.completed = step.status === 'completed';
        step.result = simulation.summary.consensus;
        step.issues = simulation.summary.conflicts;
        step.recommendations = simulation.summary.recommendations;
      } else {
        // Fallback simulation without agents
        step.status = Math.random() > 0.2 ? 'completed' : 'failed';
        step.completed = step.status === 'completed';
        step.result = `Step ${step.name} ${step.status === 'completed' ? 'completed successfully' : 'encountered issues'}`;
      }

      const actualTime = step.estimatedTime * (0.8 + Math.random() * 0.4); // 80-120% of estimated time
      
      return {
        step,
        actualTime,
        tokensUsed
      };
    } catch (error) {
      console.error(`Failed to simulate step ${step.name}:`, error);
      
      step.status = 'failed';
      step.completed = false;
      step.result = `Step failed due to simulation error: ${error instanceof Error ? error.message : 'Unknown error'}`;
      
      return {
        step,
        actualTime: step.estimatedTime,
        tokensUsed: 0
      };
    }
  }

  private async analyzeAgentContributions(agents: Agent[], steps: ProjectStep[]): Promise<Array<{
    agentId: string;
    agentName: string;
    stepsInvolved: string[];
    contribution: string;
    effectiveness: number;
  }>> {
    return agents.map(agent => ({
      agentId: agent.id,
      agentName: agent.name,
      stepsInvolved: steps.map(s => s.id),
      contribution: `${agent.role} provided expertise in ${agent.expertise?.join(', ') || 'general areas'}`,
      effectiveness: 70 + Math.random() * 25 // 70-95% effectiveness
    }));
  }

  private async analyzePromptUsage(prompts: PromptVariant[], steps: ProjectStep[]): Promise<Array<{
    promptId: string;
    promptName: string;
    stepsUsed: string[];
    effectiveness: number;
  }>> {
    return prompts.map(prompt => ({
      promptId: prompt.id,
      promptName: prompt.name,
      stepsUsed: steps.slice(0, Math.ceil(steps.length / 2)).map(s => s.id),
      effectiveness: 65 + Math.random() * 30 // 65-95% effectiveness
    }));
  }

  private async generateProjectInsights(steps: ProjectStep[], request: ProjectSimulationRequest): Promise<{
    strengths: string[];
    weaknesses: string[];
    recommendations: string[];
    riskFactors: string[];
  }> {
    const completedSteps = steps.filter(s => s.status === 'completed').length;
    const failedSteps = steps.filter(s => s.status === 'failed').length;
    
    const strengths = [];
    const weaknesses = [];
    const recommendations = [];
    const riskFactors = [];

    if (completedSteps / steps.length > 0.8) {
      strengths.push('High success rate in step completion');
    }

    if (failedSteps > 0) {
      weaknesses.push(`${failedSteps} step(s) failed during simulation`);
      recommendations.push('Review failed steps and improve planning');
    }

    if (request.complexity === 'high') {
      riskFactors.push('High complexity project requires careful monitoring');
    }

    return {
      strengths: strengths.length > 0 ? strengths : ['Project simulation completed successfully'],
      weaknesses: weaknesses.length > 0 ? weaknesses : ['No significant weaknesses identified'],
      recommendations: recommendations.length > 0 ? recommendations : ['Continue with current approach'],
      riskFactors: riskFactors.length > 0 ? riskFactors : ['Low risk project profile']
    };
  }

  private calculateOverallQuality(steps: ProjectStep[]): number {
    const completedSteps = steps.filter(s => s.status === 'completed').length;
    const totalSteps = steps.length;
    
    if (totalSteps === 0) return 0;
    
    const completionRate = completedSteps / totalSteps;
    const qualityBonus = steps.filter(s => s.recommendations && s.recommendations.length > 0).length / totalSteps * 0.1;
    
    return Math.min(100, (completionRate * 100) + (qualityBonus * 100));
  }
}

export const projectSimulationService = new ProjectSimulationService();
