
import { databaseService } from '@/lib/database';

interface Agent {
  id: string;
  name: string;
  role: string;
  systemPrompt: string;
  personality: string;
  enabled: boolean;
  expertise?: string[];
  avatar?: string;
}

interface PromptVariant {
  id: string;
  name: string;
  prompt: string;
  systemPrompt?: string;
  variables: Record<string, string>;
  purpose?: string;
  whenToUse?: string;
  whenNotToUse?: string;
  suggestions?: string;
  experimentalSubVariants?: string[];
  buildKit?: {
    frame: string;
    example: string;
  };
  compatibleAgents?: string[];
}

interface TestResult {
  id: string;
  timestamp: Date;
  type: 'prompt_test' | 'agent_simulation' | 'variation_testing' | 'project_simulation';
  data: any;
  scores?: {
    fidelity?: number;
    adherence?: number;
    consistency?: number;
    creativity?: number;
    accuracy?: number;
  };
}

class PromptStore {
  private agents: Agent[] = [];
  private prompts: PromptVariant[] = [];
  private results: TestResult[] = [];
  private listeners: (() => void)[] = [];
  private initialized = false;

  private defaultAgents: Agent[] = [
    {
      id: '1',
      name: 'Code Reviewer',
      role: 'Senior Developer',
      systemPrompt: 'You are a senior developer conducting code reviews. Focus on code quality, best practices, and potential issues.',
      personality: 'Thorough, constructive, detail-oriented',
      enabled: true,
      expertise: ['React', 'TypeScript', 'Testing', 'Architecture'],
      avatar: '👨‍💻'
    },
    {
      id: '2',
      name: 'Product Manager',
      role: 'Product Strategy',
      systemPrompt: 'You are a product manager evaluating features from a business perspective. Consider user needs and market impact.',
      personality: 'Strategic, user-focused, data-driven',
      enabled: true,
      expertise: ['Strategy', 'User Research', 'Analytics', 'Roadmapping'],
      avatar: '📊'
    },
    {
      id: '3',
      name: 'UX Designer',
      role: 'User Experience',
      systemPrompt: 'You are a UX designer focused on creating intuitive and accessible user experiences.',
      personality: 'Creative, empathetic, user-centric',
      enabled: false,
      expertise: ['Design Systems', 'Accessibility', 'User Testing', 'Prototyping'],
      avatar: '🎨'
    }
  ];

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await databaseService.initialize();
      await this.loadFromDatabase();
      this.initialized = true;
    } catch (error) {
      console.error('Failed to initialize database, using memory storage:', error);
      this.agents = [...this.defaultAgents];
      this.initialized = true;
    }
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      const [agents, prompts, results] = await Promise.all([
        databaseService.getAll('agents'),
        databaseService.getAll('prompts'),
        databaseService.getAll('results')
      ]);

      this.agents = agents.length > 0 ? agents : [...this.defaultAgents];
      this.prompts = prompts;
      this.results = results;

      // If no agents in database, save default ones
      if (agents.length === 0) {
        for (const agent of this.defaultAgents) {
          await databaseService.add('agents', agent);
        }
      }
    } catch (error) {
      console.error('Failed to load from database:', error);
      this.agents = [...this.defaultAgents];
    }
  }

  // Agent methods
  getAgents(): Agent[] {
    return [...this.agents];
  }

  getEnabledAgents(): Agent[] {
    return this.agents.filter(agent => agent.enabled);
  }

  addAgent(agent: Omit<Agent, 'id'>): Agent {
    const newAgent: Agent = {
      ...agent,
      id: Date.now().toString()
    };
    this.agents.push(newAgent);
    this.notifyListeners();
    return newAgent;
  }

  updateAgent(id: string, updates: Partial<Agent>): void {
    const index = this.agents.findIndex(agent => agent.id === id);
    if (index !== -1) {
      this.agents[index] = { ...this.agents[index], ...updates };
      this.notifyListeners();
    }
  }

  deleteAgent(id: string): void {
    this.agents = this.agents.filter(agent => agent.id !== id);
    this.notifyListeners();
  }

  toggleAgent(id: string): void {
    const agent = this.agents.find(a => a.id === id);
    if (agent) {
      agent.enabled = !agent.enabled;
      this.notifyListeners();
    }
  }

  // Prompt methods
  getPrompts(): PromptVariant[] {
    return [...this.prompts];
  }

  addPrompt(prompt: Omit<PromptVariant, 'id'>): PromptVariant {
    const newPrompt: PromptVariant = {
      ...prompt,
      id: Date.now().toString()
    };
    this.prompts.push(newPrompt);
    this.notifyListeners();
    return newPrompt;
  }

  updatePrompt(id: string, updates: Partial<PromptVariant>): void {
    const index = this.prompts.findIndex(prompt => prompt.id === id);
    if (index !== -1) {
      this.prompts[index] = { ...this.prompts[index], ...updates };
      this.notifyListeners();
    }
  }

  deletePrompt(id: string): void {
    this.prompts = this.prompts.filter(prompt => prompt.id !== id);
    this.notifyListeners();
  }

  // Results methods
  getResults(): TestResult[] {
    return [...this.results].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  addResult(result: Omit<TestResult, 'id'>): void {
    const newResult: TestResult = {
      ...result,
      id: Date.now().toString()
    };
    this.results.unshift(newResult);
    this.notifyListeners();
  }

  // Subscription methods
  subscribe(listener: () => void): () => void {
    this.listeners.push(listener);
    return () => {
      this.listeners = this.listeners.filter(l => l !== listener);
    };
  }

  private notifyListeners(): void {
    this.listeners.forEach(listener => listener());
  }

  // Cross-feature integration methods
  getCompatibleAgents(promptId: string): Agent[] {
    const prompt = this.prompts.find(p => p.id === promptId);
    if (!prompt?.compatibleAgents) return this.getEnabledAgents();
    
    return this.agents.filter(agent => 
      prompt.compatibleAgents!.includes(agent.id) && agent.enabled
    );
  }

  runIntegratedTest(promptIds: string[], agentIds: string[], scenario?: string): TestResult {
    const prompts = this.prompts.filter(p => promptIds.includes(p.id));
    const agents = this.agents.filter(a => agentIds.includes(a.id));
    
    const result: TestResult = {
      id: Date.now().toString(),
      timestamp: new Date(),
      type: 'project_simulation',
      data: {
        prompts,
        agents,
        scenario,
        results: prompts.map(prompt => ({
          promptId: prompt.id,
          agentResponses: agents.map(agent => ({
            agentId: agent.id,
            response: `Simulated response from ${agent.name} for prompt: ${prompt.name}`,
            confidence: Math.random() * 100,
            sentiment: Math.random() > 0.5 ? 'positive' : 'constructive'
          }))
        }))
      },
      scores: {
        fidelity: Math.random() * 100,
        adherence: Math.random() * 100,
        consistency: Math.random() * 100,
        creativity: Math.random() * 100,
        accuracy: Math.random() * 100
      }
    };

    this.addResult(result);
    return result;
  }
}

export const promptStore = new PromptStore();
export type { Agent, PromptVariant, TestResult };
