import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  X, 
  ArrowRight, 
  ArrowLeft, 
  CheckCircle, 
  Lightbulb,
  Zap,
  Users,
  GitBranch,
  Code,
  BarChart3,
  Settings
} from 'lucide-react';

interface TourStep {
  id: string;
  title: string;
  description: string;
  target?: string;
  icon: React.ComponentType<any>;
  action?: {
    label: string;
    onClick: () => void;
  };
  tips?: string[];
}

interface OnboardingTourProps {
  isOpen: boolean;
  onClose: () => void;
  onViewChange?: (view: string) => void;
}

export const OnboardingTour = ({ isOpen, onClose, onViewChange }: OnboardingTourProps) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [completedSteps, setCompletedSteps] = useState<Set<string>>(new Set());

  const tourSteps: TourStep[] = [
    {
      id: 'welcome',
      title: 'Welcome to Prompt Studio',
      description: 'Your comprehensive AI prompt engineering and testing platform. Let\'s take a quick tour of the key features.',
      icon: Zap,
      tips: [
        'Test prompts across multiple AI models',
        'Create and manage AI agents',
        'Build complex prompt workflows',
        'Export your work in multiple formats'
      ]
    },
    {
      id: 'prompt-tester',
      title: 'Prompt Tester',
      description: 'Test your prompts across different AI models and compare responses. Perfect for finding the best model for your use case.',
      target: 'tester',
      icon: Zap,
      action: {
        label: 'Try Prompt Tester',
        onClick: () => onViewChange?.('tester')
      },
      tips: [
        'Test with OpenAI GPT-4, Claude, and more',
        'Compare responses side by side',
        'Automatic scoring and analysis',
        'Variable substitution support'
      ]
    },
    {
      id: 'prompt-variations',
      title: 'Prompt Variations',
      description: 'Create and test multiple variations of your prompts to find the most effective wording and approach.',
      target: 'variations',
      icon: GitBranch,
      action: {
        label: 'Explore Variations',
        onClick: () => onViewChange?.('variations')
      },
      tips: [
        'A/B test different prompt approaches',
        'Statistical comparison of results',
        'Automatic variation generation',
        'Performance tracking over time'
      ]
    },
    {
      id: 'agent-studio',
      title: 'Agent Studio',
      description: 'Create AI agents with specific personalities and expertise. Simulate conversations and test agent responses.',
      target: 'agents',
      icon: Users,
      action: {
        label: 'Meet the Agents',
        onClick: () => onViewChange?.('agents')
      },
      tips: [
        'Pre-built agents for common roles',
        'Custom agent creation',
        'Personality and expertise settings',
        'Multi-agent conversations'
      ]
    },
    {
      id: 'project-simulator',
      title: 'Project Simulator',
      description: 'Run complex multi-step simulations with multiple agents and prompts working together.',
      target: 'projects',
      icon: Code,
      action: {
        label: 'Run Simulations',
        onClick: () => onViewChange?.('projects')
      },
      tips: [
        'Multi-agent collaboration',
        'Complex scenario testing',
        'Real-world project simulation',
        'Comprehensive result analysis'
      ]
    },
    {
      id: 'chain-linker',
      title: 'Chain Linker Canvas',
      description: 'Build visual prompt workflows with drag-and-drop nodes. Perfect for complex, multi-step AI processes.',
      target: 'chain-linker',
      icon: GitBranch,
      action: {
        label: 'Build Workflows',
        onClick: () => onViewChange?.('chain-linker')
      },
      tips: [
        'Visual workflow builder',
        'Drag-and-drop interface',
        'Node-based prompt chaining',
        'Conditional logic support'
      ]
    },
    {
      id: 'results-dashboard',
      title: 'Results Dashboard',
      description: 'View, analyze, and export all your test results. Track performance over time and identify trends.',
      target: 'results',
      icon: BarChart3,
      action: {
        label: 'View Results',
        onClick: () => onViewChange?.('results')
      },
      tips: [
        'Comprehensive result analysis',
        'Performance metrics and scoring',
        'Export in multiple formats',
        'Historical trend analysis'
      ]
    },
    {
      id: 'settings',
      title: 'Settings & Configuration',
      description: 'Configure your AI API keys, adjust scoring settings, and manage your data.',
      target: 'settings',
      icon: Settings,
      action: {
        label: 'Configure Settings',
        onClick: () => onViewChange?.('settings')
      },
      tips: [
        'AI provider configuration',
        'Auto-scoring customization',
        'Data backup and export',
        'Performance monitoring'
      ]
    }
  ];

  const currentStepData = tourSteps[currentStep];
  const progress = ((currentStep + 1) / tourSteps.length) * 100;

  const handleNext = () => {
    if (currentStep < tourSteps.length - 1) {
      setCompletedSteps(prev => new Set([...prev, currentStepData.id]));
      setCurrentStep(currentStep + 1);
    } else {
      handleComplete();
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleComplete = () => {
    setCompletedSteps(prev => new Set([...prev, currentStepData.id]));
    localStorage.setItem('prompt-studio-onboarding-completed', 'true');
    onClose();
  };

  const handleSkip = () => {
    localStorage.setItem('prompt-studio-onboarding-skipped', 'true');
    onClose();
  };

  const jumpToStep = (stepIndex: number) => {
    setCurrentStep(stepIndex);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-2xl bg-slate-800 border-slate-700 shadow-2xl">
        <CardHeader className="relative">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-600/20 rounded-lg">
                <currentStepData.icon className="w-6 h-6 text-blue-400" />
              </div>
              <div>
                <CardTitle className="text-xl text-slate-200">
                  {currentStepData.title}
                </CardTitle>
                <CardDescription className="text-slate-400">
                  Step {currentStep + 1} of {tourSteps.length}
                </CardDescription>
              </div>
            </div>
            <Button
              onClick={onClose}
              variant="ghost"
              size="sm"
              className="text-slate-400 hover:text-slate-200"
            >
              <X className="w-4 h-4" />
            </Button>
          </div>

          <Progress value={progress} className="mt-4" />
        </CardHeader>

        <CardContent className="space-y-6">
          <p className="text-slate-300 leading-relaxed">
            {currentStepData.description}
          </p>

          {currentStepData.tips && (
            <div className="bg-slate-900/50 p-4 rounded-lg">
              <div className="flex items-center gap-2 mb-3">
                <Lightbulb className="w-4 h-4 text-yellow-400" />
                <span className="text-sm font-medium text-yellow-400">Key Features</span>
              </div>
              <ul className="space-y-2">
                {currentStepData.tips.map((tip, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm text-slate-300">
                    <CheckCircle className="w-4 h-4 text-green-400 mt-0.5 flex-shrink-0" />
                    {tip}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {currentStepData.action && (
            <div className="flex justify-center">
              <Button
                onClick={currentStepData.action.onClick}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {currentStepData.action.label}
                <ArrowRight className="w-4 h-4 ml-2" />
              </Button>
            </div>
          )}

          {/* Step indicators */}
          <div className="flex justify-center gap-2">
            {tourSteps.map((step, index) => (
              <button
                key={step.id}
                onClick={() => jumpToStep(index)}
                className={`w-3 h-3 rounded-full transition-colors ${
                  index === currentStep
                    ? 'bg-blue-400'
                    : completedSteps.has(step.id)
                    ? 'bg-green-400'
                    : 'bg-slate-600 hover:bg-slate-500'
                }`}
                title={step.title}
              />
            ))}
          </div>

          {/* Navigation */}
          <div className="flex items-center justify-between pt-4 border-t border-slate-700">
            <div className="flex gap-2">
              <Button
                onClick={handleSkip}
                variant="ghost"
                className="text-slate-400 hover:text-slate-200"
              >
                Skip Tour
              </Button>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={handlePrevious}
                disabled={currentStep === 0}
                variant="outline"
                className="border-slate-600"
              >
                <ArrowLeft className="w-4 h-4 mr-2" />
                Previous
              </Button>
              
              <Button
                onClick={handleNext}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {currentStep === tourSteps.length - 1 ? (
                  <>
                    Complete Tour
                    <CheckCircle className="w-4 h-4 ml-2" />
                  </>
                ) : (
                  <>
                    Next
                    <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

// Hook to manage onboarding state
export const useOnboarding = () => {
  const [showOnboarding, setShowOnboarding] = useState(false);

  useEffect(() => {
    const hasCompletedOnboarding = localStorage.getItem('prompt-studio-onboarding-completed');
    const hasSkippedOnboarding = localStorage.getItem('prompt-studio-onboarding-skipped');
    
    if (!hasCompletedOnboarding && !hasSkippedOnboarding) {
      // Show onboarding after a short delay
      const timer = setTimeout(() => {
        setShowOnboarding(true);
      }, 1000);
      
      return () => clearTimeout(timer);
    }
  }, []);

  const startOnboarding = () => {
    setShowOnboarding(true);
  };

  const closeOnboarding = () => {
    setShowOnboarding(false);
  };

  const resetOnboarding = () => {
    localStorage.removeItem('prompt-studio-onboarding-completed');
    localStorage.removeItem('prompt-studio-onboarding-skipped');
    setShowOnboarding(true);
  };

  return {
    showOnboarding,
    startOnboarding,
    closeOnboarding,
    resetOnboarding
  };
};
