import { describe, it, expect, vi, beforeEach } from 'vitest';
import { aiService } from '../ai-service';

// Mock fetch globally
global.fetch = vi.fn();

describe('AIService', () => {
  beforeEach(() => {
    vi.clearAllMocks();
    // Reset environment variables
    process.env.VITE_OPENAI_API_KEY = 'test-openai-key';
    process.env.VITE_ANTHROPIC_API_KEY = 'test-anthropic-key';
  });

  describe('generateResponse', () => {
    it('should generate response with OpenAI', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response from OpenAI'
          }
        }],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(result.content).toBe('Test response from OpenAI');
      expect(result.provider).toBe('openai');
      expect(result.model).toBe('gpt-4');
      expect(result.usage).toEqual({
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30
      });
    });

    it('should generate response with Anthropic', async () => {
      const mockResponse = {
        content: [{
          text: 'Test response from Anthropic'
        }],
        usage: {
          input_tokens: 15,
          output_tokens: 25
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await aiService.generateResponse({
        provider: 'anthropic',
        model: 'claude-3-sonnet',
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(result.content).toBe('Test response from Anthropic');
      expect(result.provider).toBe('anthropic');
      expect(result.model).toBe('claude-3-sonnet');
      expect(result.usage).toEqual({
        promptTokens: 15,
        completionTokens: 25,
        totalTokens: 40
      });
    });

    it('should handle API errors gracefully', async () => {
      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      })).rejects.toThrow('OpenAI API error: 401 Unauthorized');
    });

    it('should handle network errors', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      })).rejects.toThrow('Network error');
    });

    it('should validate required parameters', async () => {
      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: []
      })).rejects.toThrow('Messages array cannot be empty');
    });

    it('should use default parameters when not provided', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response'
          }
        }],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      await aiService.generateResponse({
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-openai-key'
          }),
          body: expect.stringContaining('"temperature":0.7')
        })
      );
    });
  });

  describe('validateConfiguration', () => {
    it('should validate OpenAI configuration', () => {
      const result = aiService.validateConfiguration('openai');
      expect(result.valid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing API keys', () => {
      delete process.env.VITE_OPENAI_API_KEY;
      
      const result = aiService.validateConfiguration('openai');
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('OpenAI API key is not configured');
    });

    it('should validate unsupported providers', () => {
      const result = aiService.validateConfiguration('unsupported' as any);
      expect(result.valid).toBe(false);
      expect(result.errors).toContain('Unsupported AI provider: unsupported');
    });
  });

  describe('getAvailableModels', () => {
    it('should return available models for valid provider', () => {
      const models = aiService.getAvailableModels('openai');
      expect(models).toContain('gpt-4');
      expect(models).toContain('gpt-3.5-turbo');
    });

    it('should return empty array for invalid provider', () => {
      const models = aiService.getAvailableModels('invalid' as any);
      expect(models).toEqual([]);
    });
  });

  describe('estimateTokens', () => {
    it('should estimate tokens for text', () => {
      const tokens = aiService.estimateTokens('Hello world');
      expect(tokens).toBeGreaterThan(0);
      expect(typeof tokens).toBe('number');
    });

    it('should return 0 for empty text', () => {
      const tokens = aiService.estimateTokens('');
      expect(tokens).toBe(0);
    });

    it('should handle long text', () => {
      const longText = 'word '.repeat(1000);
      const tokens = aiService.estimateTokens(longText);
      expect(tokens).toBeGreaterThan(1000);
    });
  });

  describe('formatMessages', () => {
    it('should format messages correctly for OpenAI', () => {
      const messages = [
        { role: 'system' as const, content: 'You are a helpful assistant' },
        { role: 'user' as const, content: 'Hello' }
      ];

      const formatted = aiService.formatMessages(messages, 'openai');
      expect(formatted).toEqual(messages);
    });

    it('should format messages correctly for Anthropic', () => {
      const messages = [
        { role: 'system' as const, content: 'You are a helpful assistant' },
        { role: 'user' as const, content: 'Hello' }
      ];

      const formatted = aiService.formatMessages(messages, 'anthropic');
      expect(formatted).toHaveLength(1);
      expect(formatted[0].role).toBe('user');
      expect(formatted[0].content).toContain('You are a helpful assistant');
      expect(formatted[0].content).toContain('Hello');
    });
  });
});
