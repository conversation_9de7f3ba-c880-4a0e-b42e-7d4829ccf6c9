import { describe, it, expect, vi, beforeEach } from 'vitest';
import { aiService } from '../ai-service';

// Mock fetch globally
global.fetch = vi.fn();

describe('AIService', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Mock localStorage
    const mockLocalStorage = (() => {
      let store: Record<string, string> = {};
      return {
        getItem: (key: string) => store[key] || null,
        setItem: (key: string, value: string) => { store[key] = value.toString(); },
        removeItem: (key: string) => { delete store[key]; },
        clear: () => { store = {}; }
      };
    })();
    Object.defineProperty(window, 'localStorage', { value: mockLocalStorage, writable: true });

    // Reset environment variables
    process.env.VITE_OPENAI_API_KEY = 'test-openai-key';
    process.env.VITE_ANTHROPIC_API_KEY = 'test-anthropic-key';
    process.env.VITE_GOOGLE_API_KEY = 'test-google-key'; // Add Google API Key
    process.env.VITE_DEFAULT_AI_PROVIDER = 'openai'; // Set defaults for testing
    process.env.VITE_DEFAULT_MODEL = 'gpt-4';
    process.env.VITE_DEFAULT_TEMPERATURE = '0.7';
    process.env.VITE_DEFAULT_MAX_TOKENS = '2048';

    // Clear localStorage before each test to ensure isolation
    localStorage.clear();
    // Manually refresh configuration as AIService instance is created once
    // and constructor (which calls loadConfiguration) runs before this beforeEach.
    aiService.refreshConfiguration();
  });

  describe('generateResponse', () => {
    it('should generate response with OpenAI', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response from OpenAI'
          }
        }],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(result.content).toBe('Test response from OpenAI');
      expect(result.provider).toBe('openai');
      expect(result.model).toBe('gpt-4');
      expect(result.usage).toEqual({
        promptTokens: 10,
        completionTokens: 20,
        totalTokens: 30
      });
    });

    it('should generate response with Anthropic', async () => {
      const mockResponse = {
        content: [{
          text: 'Test response from Anthropic'
        }],
        usage: {
          input_tokens: 15,
          output_tokens: 25
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await aiService.generateResponse({
        provider: 'anthropic',
        model: 'claude-3-sonnet',
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(result.content).toBe('Test response from Anthropic');
      expect(result.provider).toBe('anthropic');
      expect(result.model).toBe('claude-3-sonnet');
      expect(result.usage).toEqual({
        promptTokens: 15,
        completionTokens: 25,
        totalTokens: 40
      });
    });

    it('should generate response with Google Gemini', async () => {
      // Ensure the Google API key is set for this test, overriding beforeEach if necessary
      process.env.VITE_GOOGLE_API_KEY = 'test-gemini-key-specific';
      aiService.refreshConfiguration(); // Ensure AIService reloads the new key

      const mockResponse = {
        candidates: [{ content: { parts: [{ text: 'Test response from Gemini' }] } }],
        usageMetadata: { promptTokenCount: 5, candidatesTokenCount: 15, totalTokenCount: 20 }
      };
      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      const result = await aiService.generateResponse({
        provider: 'google',
        model: 'gemini-1.5-pro-latest',
        messages: [{ role: 'user', content: 'Test prompt for Gemini' }]
      });

      expect(result.content).toBe('Test response from Gemini');
      expect(result.provider).toBe('google');
      expect(result.model).toBe('gemini-1.5-pro-latest');
      expect(result.usage).toEqual({
        promptTokens: 5,
        completionTokens: 15,
        totalTokens: 20
      });
      expect(fetch).toHaveBeenCalledWith(
        expect.stringContaining('https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-pro-latest:generateContent?key=test-gemini-key-specific'),
        expect.any(Object)
      );
    });

    it('should handle API errors gracefully', async () => {
      (fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 401,
        statusText: 'Unauthorized'
      });

      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      })).rejects.toThrow('OpenAI API error: 401 Unauthorized');
    });

    it('should handle network errors', async () => {
      (fetch as any).mockRejectedValueOnce(new Error('Network error'));

      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: [{ role: 'user', content: 'Test prompt' }]
      })).rejects.toThrow('Network error');
    });

    it('should validate required parameters', async () => {
      await expect(aiService.generateResponse({
        provider: 'openai',
        model: 'gpt-4',
        messages: []
      })).rejects.toThrow('Messages array cannot be empty');
    });

    it('should use default parameters when not provided', async () => {
      const mockResponse = {
        choices: [{
          message: {
            content: 'Test response'
          }
        }],
        usage: {
          prompt_tokens: 10,
          completion_tokens: 20,
          total_tokens: 30
        }
      };

      (fetch as any).mockResolvedValueOnce({
        ok: true,
        json: async () => mockResponse
      });

      await aiService.generateResponse({
        messages: [{ role: 'user', content: 'Test prompt' }]
      });

      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-openai-key'
          }),
          body: expect.stringContaining('"temperature":0.7')
        })
      );
    });
  });

  describe('getModelsForProvider', () => {
    it('should return available models for OpenAI including gpt-4o', () => {
      const models = aiService.getModelsForProvider('openai');
      expect(models).toContain('gpt-4o');
      expect(models).toContain('gpt-4');
      expect(models).toContain('gpt-3.5-turbo');
    });

    it('should return available models for Anthropic including claude-3.5-sonnet', () => {
      const models = aiService.getModelsForProvider('anthropic');
      expect(models).toContain('claude-3.5-sonnet');
      expect(models).toContain('claude-3-opus');
    });

    it('should return available models for Google Gemini', () => {
      const models = aiService.getModelsForProvider('google');
      expect(models).toContain('gemini-1.5-pro-latest');
      expect(models).toContain('gemini-1.5-flash-latest');
    });

    it('should return empty array for invalid provider', () => {
      const models = aiService.getModelsForProvider('invalid' as any);
      expect(models).toEqual([]);
    });
  });
});

describe('Configuration Loading', () => {
  beforeEach(() => {
    // Ensure a clean slate for each test in this suite
    localStorage.clear();
    // Set specific env vars for this suite; some might be deleted in tests
    process.env.VITE_OPENAI_API_KEY = 'env-openai-key';
    process.env.VITE_ANTHROPIC_API_KEY = 'env-anthropic-key';
    process.env.VITE_GOOGLE_API_KEY = 'env-google-key';
    process.env.VITE_DEFAULT_AI_PROVIDER = 'openai';
    process.env.VITE_DEFAULT_MODEL = 'gpt-4o'; // Use a new model
    process.env.VITE_DEFAULT_TEMPERATURE = '0.5';
    process.env.VITE_DEFAULT_MAX_TOKENS = '1024';
    aiService.refreshConfiguration(); // Load the above env vars
  });

  it('should load API keys and default settings from environment variables if localStorage is empty', () => {
    expect(aiService.isConfigured('openai')).toBe(true);
    expect(aiService.isConfigured('anthropic')).toBe(true);
    expect(aiService.isConfigured('google')).toBe(true);

    const defaults = aiService.getDefaultSettings();
    expect(defaults.provider).toBe('openai');
    expect(defaults.model).toBe('gpt-4o');
    expect(defaults.temperature).toBe(0.5);
    expect(defaults.maxTokens).toBe(1024);
  });

  it('should load API keys and settings from localStorage, overriding env vars', () => {
    const lsSettings = {
      openaiApiKey: 'ls-openai-key',
      anthropicApiKey: 'ls-anthropic-key',
      googleApiKey: 'ls-google-key',
      provider: 'google', // Different from env var default
      model: 'gemini-1.5-pro-latest', // Different from env var default
      temperature: 0.9,
      maxTokens: 512
    };
    localStorage.setItem('ai-settings', JSON.stringify(lsSettings));
    aiService.refreshConfiguration(); // Reload with localStorage taking precedence

    // Check that isConfigured uses the keys now loaded into the service instance
    expect(aiService.isConfigured('openai')).toBe(true);
    expect(aiService.isConfigured('anthropic')).toBe(true);
    expect(aiService.isConfigured('google')).toBe(true);

    const defaults = aiService.getDefaultSettings(); // getDefaultSettings reflects the loaded config
    expect(defaults.provider).toBe('google');
    expect(defaults.model).toBe('gemini-1.5-pro-latest');
    expect(defaults.temperature).toBe(0.9);
    expect(defaults.maxTokens).toBe(512);
  });

  it('should correctly report not configured if keys are missing from both localStorage and env vars', () => {
    localStorage.clear();
    delete process.env.VITE_OPENAI_API_KEY;
    delete process.env.VITE_ANTHROPIC_API_KEY;
    delete process.env.VITE_GOOGLE_API_KEY;
    // Ensure no default values are picked up if keys are truly absent
    aiService.refreshConfiguration();

    expect(aiService.isConfigured('openai')).toBe(false);
    expect(aiService.isConfigured('anthropic')).toBe(false);
    expect(aiService.isConfigured('google')).toBe(false);
  });

  it('should use env var if localStorage has an empty string for an API key', () => {
    const lsSettings = {
      openaiApiKey: '', // Empty string in localStorage
      provider: 'openai',
      model: 'gpt-4o'
    };
    localStorage.setItem('ai-settings', JSON.stringify(lsSettings));
    // process.env.VITE_OPENAI_API_KEY is 'env-openai-key' from this describe's beforeEach
    aiService.refreshConfiguration();

    expect(aiService.isConfigured('openai')).toBe(true); // Should pick up the env var key
  });

  it('should use hardcoded defaults if both localStorage and env vars are missing for non-key settings', () => {
    localStorage.clear();
    delete process.env.VITE_DEFAULT_AI_PROVIDER;
    delete process.env.VITE_DEFAULT_MODEL;
    delete process.env.VITE_DEFAULT_TEMPERATURE;
    delete process.env.VITE_DEFAULT_MAX_TOKENS;
    // Keys are still set from the outer describe's beforeEach for isConfigured to pass if needed
    // but we are testing the default provider/model etc.
    aiService.refreshConfiguration();
    const defaults = aiService.getDefaultSettings();

    expect(defaults.provider).toBe('openai'); // Default hardcoded in AIService
    expect(defaults.model).toBe('gpt-4');     // Default hardcoded in AIService
    expect(defaults.temperature).toBe(0.7);   // Default hardcoded in AIService
    expect(defaults.maxTokens).toBe(2048);    // Default hardcoded in AIService
  });
});
