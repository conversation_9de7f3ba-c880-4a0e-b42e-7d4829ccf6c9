
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Checkbox } from '@/components/ui/checkbox';
import { Code, FileText, GitBranch, Clock, CheckCircle, AlertCircle } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface ProjectStep {
  id: string;
  name: string;
  description: string;
  estimatedTime: number;
  dependencies: string[];
  completed: boolean;
}

interface ProjectSimulation {
  id: string;
  name: string;
  description: string;
  projectType: string;
  steps: ProjectStep[];
  totalEstimatedTime: number;
  complexity: 'low' | 'medium' | 'high';
}

export const ProjectSimulator = () => {
  const [simulations, setSimulations] = useState<ProjectSimulation[]>([
    {
      id: '1',
      name: 'E-commerce Website',
      description: 'Build a full-stack e-commerce platform with user authentication, product catalog, and payment processing',
      projectType: 'Web Application',
      complexity: 'high',
      totalEstimatedTime: 480, // 8 weeks
      steps: [
        {
          id: 's1',
          name: 'Project Setup & Planning',
          description: 'Initialize repository, set up development environment, define requirements',
          estimatedTime: 24,
          dependencies: [],
          completed: false
        },
        {
          id: 's2',
          name: 'Database Design',
          description: 'Design and implement database schema for users, products, orders',
          estimatedTime: 32,
          dependencies: ['s1'],
          completed: false
        },
        {
          id: 's3',
          name: 'Authentication System',
          description: 'Implement user registration, login, and session management',
          estimatedTime: 48,
          dependencies: ['s2'],
          completed: false
        },
        {
          id: 's4',
          name: 'Product Catalog',
          description: 'Build product listing, search, and filtering functionality',
          estimatedTime: 64,
          dependencies: ['s2'],
          completed: false
        },
        {
          id: 's5',
          name: 'Shopping Cart & Checkout',
          description: 'Implement cart functionality and checkout process',
          estimatedTime: 72,
          dependencies: ['s3', 's4'],
          completed: false
        }
      ]
    }
  ]);

  const [selectedSimulation, setSelectedSimulation] = useState<string | null>(null);
  const [isRunning, setIsRunning] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);

  const handleRunSimulation = async (simulationId: string) => {
    const simulation = simulations.find(s => s.id === simulationId);
    if (!simulation) return;

    setSelectedSimulation(simulationId);
    setIsRunning(true);
    setCurrentStep(0);

    // Reset all steps
    setSimulations(prev => prev.map(sim => 
      sim.id === simulationId 
        ? { ...sim, steps: sim.steps.map(step => ({ ...step, completed: false })) }
        : sim
    ));

    // Simulate step-by-step completion
    for (let i = 0; i < simulation.steps.length; i++) {
      setCurrentStep(i);
      
      // Simulate work time (faster for demo)
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      // Mark step as completed
      setSimulations(prev => prev.map(sim => 
        sim.id === simulationId 
          ? { 
              ...sim, 
              steps: sim.steps.map((step, index) => 
                index === i ? { ...step, completed: true } : step
              )
            }
          : sim
      ));
    }

    setIsRunning(false);
    toast({
      title: "Simulation Complete",
      description: `${simulation.name} simulation finished successfully`,
    });
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'bg-green-600';
      case 'medium': return 'bg-yellow-600';
      case 'high': return 'bg-red-600';
      default: return 'bg-slate-600';
    }
  };

  const getStepIcon = (step: ProjectStep, isActive: boolean) => {
    if (step.completed) return <CheckCircle className="w-4 h-4 text-green-400" />;
    if (isActive) return <Clock className="w-4 h-4 text-blue-400 animate-pulse" />;
    return <AlertCircle className="w-4 h-4 text-slate-400" />;
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200">Project Simulator</h2>
        <Badge variant="outline" className="border-slate-500 text-slate-300">
          Simulated Development Outcomes
        </Badge>
      </div>

      <div className="grid gap-6">
        {simulations.map((simulation) => {
          const completedSteps = simulation.steps.filter(s => s.completed).length;
          const progress = (completedSteps / simulation.steps.length) * 100;
          const isActive = selectedSimulation === simulation.id && isRunning;

          return (
            <Card key={simulation.id} className="bg-slate-800/50 border-slate-700 p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center gap-3">
                  <Code className="w-6 h-6 text-blue-400" />
                  <div>
                    <h3 className="text-lg font-semibold text-slate-200">{simulation.name}</h3>
                    <p className="text-sm text-slate-400">{simulation.description}</p>
                    <div className="flex gap-2 mt-2">
                      <Badge variant="outline" className="border-slate-500 text-slate-300">
                        {simulation.projectType}
                      </Badge>
                      <Badge className={`${getComplexityColor(simulation.complexity)} text-white`}>
                        {simulation.complexity} complexity
                      </Badge>
                      <Badge variant="outline" className="border-slate-500 text-slate-300">
                        ~{Math.round(simulation.totalEstimatedTime / 8)} weeks
                      </Badge>
                    </div>
                  </div>
                </div>
                <Button
                  onClick={() => handleRunSimulation(simulation.id)}
                  disabled={isActive}
                  className="bg-purple-600 hover:bg-purple-700"
                >
                  {isActive ? 'Running...' : 'Simulate'}
                </Button>
              </div>

              {/* Progress Bar */}
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-slate-400">Progress</span>
                  <span className="text-slate-300">{completedSteps}/{simulation.steps.length} steps</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              {/* Steps */}
              <div className="space-y-3">
                {simulation.steps.map((step, index) => {
                  const isActiveStep = isActive && currentStep === index;
                  
                  return (
                    <div 
                      key={step.id}
                      className={`flex items-start gap-3 p-3 rounded ${
                        isActiveStep ? 'bg-blue-900/30 border border-blue-600/30' : 'bg-slate-900/30'
                      }`}
                    >
                      {getStepIcon(step, isActiveStep)}
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className={`font-medium ${
                            step.completed ? 'text-green-300' : 
                            isActiveStep ? 'text-blue-300' : 'text-slate-300'
                          }`}>
                            {step.name}
                          </h4>
                          <span className="text-xs text-slate-400">
                            {step.estimatedTime}h
                          </span>
                        </div>
                        <p className="text-sm text-slate-400 mt-1">{step.description}</p>
                        {step.dependencies.length > 0 && (
                          <div className="flex items-center gap-2 mt-2">
                            <GitBranch className="w-3 h-3 text-slate-500" />
                            <span className="text-xs text-slate-500">
                              Depends on: {step.dependencies.join(', ')}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            </Card>
          );
        })}
      </div>

      {/* Simulation Insights */}
      <Card className="bg-slate-800/50 border-slate-700 p-6">
        <h3 className="text-lg font-semibold text-slate-200 mb-4">Simulation Insights</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-400">85%</div>
            <div className="text-sm text-slate-400">Average Success Rate</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">12.3%</div>
            <div className="text-sm text-slate-400">Time Saved vs Manual</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">94%</div>
            <div className="text-sm text-slate-400">Code Quality Score</div>
          </div>
        </div>
      </Card>
    </div>
  );
};
