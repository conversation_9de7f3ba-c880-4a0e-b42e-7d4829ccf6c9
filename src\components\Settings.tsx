
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from '@/components/ui/tabs';
import { Key, Database, Bell, Palette, Settings as SettingsIcon } from 'lucide-react';
import { useState } from 'react';
import { toast } from '@/hooks/use-toast';
import { AIConfiguration } from './AIConfiguration';
import { AutoScoringToggle } from './AutoScoringToggle';

export const Settings = () => {
  const [apiKeys, setApiKeys] = useState({
    openai: '',
    anthropic: '',
    gemini: '',
    mistral: ''
  });

  const [preferences, setPreferences] = useState({
    autoSave: true,
    notifications: true,
    darkMode: true,
    parallelExecution: true
  });

  const handleApiKeyChange = (provider: string, value: string) => {
    setApiKeys(prev => ({
      ...prev,
      [provider]: value
    }));
  };

  const handlePreferenceChange = (key: string, value: boolean) => {
    setPreferences(prev => ({
      ...prev,
      [key]: value
    }));
  };

  const handleSaveSettings = () => {
    toast({
      title: "Settings Saved",
      description: "Your configuration has been saved successfully.",
    });
  };

  return (
    <div className="space-y-6 max-w-4xl">
      <div className="flex items-center gap-2 mb-6">
        <SettingsIcon className="h-6 w-6" />
        <h2 className="text-2xl font-bold text-slate-200">Settings</h2>
      </div>

      <Tabs defaultValue="ai" className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="ai">AI Configuration</TabsTrigger>
          <TabsTrigger value="scoring">Auto Scoring</TabsTrigger>
          <TabsTrigger value="legacy">Legacy API Keys</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>

        <TabsContent value="ai" className="space-y-4">
          <AIConfiguration />
        </TabsContent>

        <TabsContent value="scoring" className="space-y-4">
          <AutoScoringToggle />
        </TabsContent>

        <TabsContent value="legacy" className="space-y-4">
          {/* Legacy API Keys Section */}
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Key className="w-5 h-5 text-blue-400" />
              <h3 className="text-lg font-semibold text-slate-200">Legacy API Keys</h3>
              <p className="text-sm text-slate-400 ml-2">(Use AI Configuration tab for better management)</p>
            </div>

            <div className="space-y-4">
              <div>
                <Label htmlFor="openai-key" className="text-slate-200">OpenAI API Key</Label>
                <Input
                  id="openai-key"
                  type="password"
                  placeholder="sk-..."
                  value={apiKeys.openai}
                  onChange={(e) => handleApiKeyChange('openai', e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label htmlFor="anthropic-key" className="text-slate-200">Anthropic API Key</Label>
                <Input
                  id="anthropic-key"
                  type="password"
                  placeholder="sk-ant-..."
                  value={apiKeys.anthropic}
                  onChange={(e) => handleApiKeyChange('anthropic', e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label htmlFor="gemini-key" className="text-slate-200">Google Gemini API Key</Label>
                <Input
                  id="gemini-key"
                  type="password"
                  placeholder="AIza..."
                  value={apiKeys.gemini}
                  onChange={(e) => handleApiKeyChange('gemini', e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white"
                />
              </div>

              <div>
                <Label htmlFor="mistral-key" className="text-slate-200">Mistral API Key</Label>
                <Input
                  id="mistral-key"
                  type="password"
                  placeholder="..."
                  value={apiKeys.mistral}
                  onChange={(e) => handleApiKeyChange('mistral', e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white"
                />
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="preferences" className="space-y-4">
          {/* Preferences Section */}
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="flex items-center gap-2 mb-4">
              <Palette className="w-5 h-5 text-purple-400" />
              <h3 className="text-lg font-semibold text-slate-200">Preferences</h3>
            </div>

            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-slate-200">Auto-save prompts</Label>
                  <p className="text-sm text-slate-400">Automatically save prompt changes</p>
                </div>
                <Switch
                  checked={preferences.autoSave}
                  onCheckedChange={(checked) => handlePreferenceChange('autoSave', checked)}
                />
              </div>

              <Separator className="bg-slate-600" />

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-slate-200">Notifications</Label>
                  <p className="text-sm text-slate-400">Receive test completion notifications</p>
                </div>
                <Switch
                  checked={preferences.notifications}
                  onCheckedChange={(checked) => handlePreferenceChange('notifications', checked)}
                />
              </div>

              <Separator className="bg-slate-600" />

              <div className="flex items-center justify-between">
                <div>
                  <Label className="text-slate-200">Parallel Execution</Label>
                  <p className="text-sm text-slate-400">Run multiple model tests simultaneously</p>
                </div>
                <Switch
                  checked={preferences.parallelExecution}
                  onCheckedChange={(checked) => handlePreferenceChange('parallelExecution', checked)}
                />
              </div>
            </div>
          </Card>

          <Button
            onClick={handleSaveSettings}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            Save Settings
          </Button>
        </TabsContent>
      </Tabs>
    </div>
  );
};
