
import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Bot, Trash2, Play, Users, Settings, Zap, Brain, Target } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { usePromptStore } from '@/hooks/usePromptStore';
import { Agent } from '@/store/promptStore';

interface AgentSimulatorProps {
  onRunSimulation: (agents: Agent[], scenario: string) => void;
}

export const AgentSimulator = ({ onRunSimulation }: AgentSimulatorProps) => {
  const { agents, enabledAgents, addAgent, updateAgent, deleteAgent, toggleAgent, prompts, runIntegratedTest } = usePromptStore();
  
  const [newAgent, setNewAgent] = useState<Partial<Agent>>({
    name: '',
    role: '',
    systemPrompt: '',
    personality: '',
    expertise: [],
    avatar: '',
    enabled: true
  });

  const [scenario, setScenario] = useState('');
  const [isAddingAgent, setIsAddingAgent] = useState(false);
  const [selectedPrompts, setSelectedPrompts] = useState<string[]>([]);
  const [newExpertise, setNewExpertise] = useState('');

  const handleAddAgent = () => {
    if (!newAgent.name || !newAgent.role || !newAgent.systemPrompt) {
      toast({
        title: "Error",
        description: "Please fill in all required fields",
        variant: "destructive"
      });
      return;
    }

    const agent = addAgent({
      name: newAgent.name || '',
      role: newAgent.role || '',
      systemPrompt: newAgent.systemPrompt || '',
      personality: newAgent.personality || '',
      expertise: newAgent.expertise || [],
      avatar: newAgent.avatar || '🤖',
      enabled: true
    });

    setNewAgent({ 
      name: '', role: '', systemPrompt: '', personality: '', 
      expertise: [], avatar: '', enabled: true 
    });
    setIsAddingAgent(false);
    toast({
      title: "Agent Created",
      description: `${agent.name} has been added to your agent library`,
    });
  };

  const handleRunSimulation = () => {
    const activeAgents = agents.filter(agent => agent.enabled);
    if (activeAgents.length === 0) {
      toast({
        title: "Error",
        description: "Please enable at least one agent",
        variant: "destructive"
      });
      return;
    }

    if (!scenario.trim()) {
      toast({
        title: "Error",
        description: "Please provide a simulation scenario",
        variant: "destructive"
      });
      return;
    }

    onRunSimulation(activeAgents, scenario);
  };

  const handleRunIntegratedTest = () => {
    if (selectedPrompts.length === 0 || enabledAgents.length === 0) {
      toast({
        title: "Error",
        description: "Please select prompts and enable agents",
        variant: "destructive"
      });
      return;
    }

    runIntegratedTest(selectedPrompts, enabledAgents.map(a => a.id), scenario);
    toast({
      title: "Integrated Test Started",
      description: "Running prompts through agent simulation",
    });
  };

  const handleAddExpertise = () => {
    if (!newExpertise.trim()) return;
    setNewAgent(prev => ({
      ...prev,
      expertise: [...(prev.expertise || []), newExpertise.trim()]
    }));
    setNewExpertise('');
  };

  const handleRemoveExpertise = (index: number) => {
    setNewAgent(prev => ({
      ...prev,
      expertise: prev.expertise?.filter((_, i) => i !== index) || []
    }));
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200 flex items-center gap-3">
          <Brain className="w-8 h-8 text-purple-400" />
          Agent Studio
        </h2>
        <Button
          onClick={() => setIsAddingAgent(true)}
          className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
        >
          <Plus className="w-4 h-4 mr-2" />
          Create Agent
        </Button>
      </div>

      <Tabs defaultValue="agents" className="w-full">
        <TabsList className="grid w-full grid-cols-3 bg-slate-800/50 border border-slate-600">
          <TabsTrigger 
            value="agents" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-purple-600 data-[state=active]:to-purple-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Users className="w-4 h-4 mr-2" />
            Agents ({agents.length})
          </TabsTrigger>
          <TabsTrigger 
            value="simulation" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-blue-600 data-[state=active]:to-blue-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Zap className="w-4 h-4 mr-2" />
            Simulation
          </TabsTrigger>
          <TabsTrigger 
            value="integration" 
            className="data-[state=active]:bg-gradient-to-r data-[state=active]:from-green-600 data-[state=active]:to-green-700 data-[state=active]:text-white data-[state=active]:shadow-lg transition-all duration-200"
          >
            <Target className="w-4 h-4 mr-2" />
            Integration
          </TabsTrigger>
        </TabsList>

        <TabsContent value="agents" className="space-y-4">
          {/* Scenario Input */}
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="space-y-4">
              <Label htmlFor="scenario" className="text-slate-200 flex items-center gap-2">
                <Zap className="w-4 h-4 text-yellow-400" />
                Global Scenario (applies to all tests)
              </Label>
              <Textarea
                id="scenario"
                placeholder="Describe the scenario you want the agents to respond to..."
                value={scenario}
                onChange={(e) => setScenario(e.target.value)}
                className="bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[100px]"
              />
            </div>
          </Card>

          {/* Agents List */}
          <div className="grid gap-4">
            {agents.map((agent) => (
              <Card key={agent.id} className="bg-slate-800/50 border-slate-700 p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Checkbox
                      checked={agent.enabled}
                      onCheckedChange={() => toggleAgent(agent.id)}
                      className="border-slate-500"
                    />
                    <div className="text-2xl">{agent.avatar || '🤖'}</div>
                    <div>
                      <h3 className="text-lg font-semibold text-slate-200">{agent.name}</h3>
                      <Badge variant="outline" className="border-purple-500 text-purple-300">
                        {agent.role}
                      </Badge>
                    </div>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => deleteAgent(agent.id)}
                    className="text-red-400 hover:text-red-300 hover:bg-red-900/20"
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
                
                <div className="space-y-3 text-sm">
                  <p className="text-slate-400">
                    <span className="font-medium">System Prompt:</span> {agent.systemPrompt.slice(0, 150)}...
                  </p>
                  {agent.personality && (
                    <p className="text-slate-400">
                      <span className="font-medium">Personality:</span> {agent.personality}
                    </p>
                  )}
                  {agent.expertise && agent.expertise.length > 0 && (
                    <div className="flex flex-wrap gap-1 mt-2">
                      <span className="text-slate-400 text-xs">Expertise:</span>
                      {agent.expertise.map((skill, index) => (
                        <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300 text-xs">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="simulation" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
              <Zap className="w-5 h-5 text-blue-400" />
              Agent Simulation Control
            </h3>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-center">
                <div>
                  <div className="text-2xl font-bold text-purple-400">{agents.length}</div>
                  <div className="text-sm text-slate-400">Total Agents</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-400">{enabledAgents.length}</div>
                  <div className="text-sm text-slate-400">Active Agents</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-400">
                    {agents.reduce((acc, a) => acc + (a.expertise?.length || 0), 0)}
                  </div>
                  <div className="text-sm text-slate-400">Skills Total</div>
                </div>
              </div>
              
              <Button 
                onClick={handleRunSimulation}
                disabled={enabledAgents.length === 0}
                className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                size="lg"
              >
                <Play className="w-4 h-4 mr-2" />
                Run Agent Simulation
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="integration" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
              <Target className="w-5 h-5 text-green-400" />
              Integrated Testing
            </h3>
            <div className="space-y-4">
              <div>
                <Label className="text-slate-200">Select Prompts to Test</Label>
                <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                  {prompts.map((prompt) => (
                    <div key={prompt.id} className="flex items-center space-x-2">
                      <Checkbox
                        id={prompt.id}
                        checked={selectedPrompts.includes(prompt.id)}
                        onCheckedChange={(checked) => {
                          if (checked) {
                            setSelectedPrompts(prev => [...prev, prompt.id]);
                          } else {
                            setSelectedPrompts(prev => prev.filter(id => id !== prompt.id));
                          }
                        }}
                      />
                      <label htmlFor={prompt.id} className="text-sm text-slate-300 cursor-pointer">
                        {prompt.name}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-center">
                <div>
                  <div className="text-xl font-bold text-blue-400">{selectedPrompts.length}</div>
                  <div className="text-sm text-slate-400">Selected Prompts</div>
                </div>
                <div>
                  <div className="text-xl font-bold text-purple-400">{enabledAgents.length}</div>
                  <div className="text-sm text-slate-400">Active Agents</div>
                </div>
              </div>

              <Button 
                onClick={handleRunIntegratedTest}
                disabled={selectedPrompts.length === 0 || enabledAgents.length === 0}
                className="w-full bg-gradient-to-r from-green-600 to-teal-600 hover:from-green-700 hover:to-teal-700"
                size="lg"
              >
                <Target className="w-4 h-4 mr-2" />
                Run Integrated Test
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Add Agent Form */}
      {isAddingAgent && (
        <Card className="bg-slate-800/50 border-slate-700 p-6">
          <h3 className="text-lg font-semibold text-slate-200 mb-4 flex items-center gap-2">
            <Plus className="w-5 h-5 text-purple-400" />
            Create New Agent
          </h3>
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="agent-name" className="text-slate-200">Agent Name *</Label>
                <Input
                  id="agent-name"
                  value={newAgent.name || ''}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, name: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="e.g., UX Researcher"
                />
              </div>
              <div>
                <Label htmlFor="agent-role" className="text-slate-200">Role *</Label>
                <Input
                  id="agent-role"
                  value={newAgent.role || ''}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, role: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="e.g., User Research & Analysis"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="agent-avatar" className="text-slate-200">Avatar Emoji</Label>
                <Input
                  id="agent-avatar"
                  value={newAgent.avatar || ''}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, avatar: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="🎨"
                />
              </div>
              <div>
                <Label htmlFor="agent-personality" className="text-slate-200">Personality</Label>
                <Input
                  id="agent-personality"
                  value={newAgent.personality || ''}
                  onChange={(e) => setNewAgent(prev => ({ ...prev, personality: e.target.value }))}
                  className="bg-slate-900/50 border-slate-600 text-white"
                  placeholder="e.g., Analytical, empathetic, detail-oriented"
                />
              </div>
            </div>
            
            <div>
              <Label htmlFor="agent-system-prompt" className="text-slate-200">System Prompt *</Label>
              <Textarea
                id="agent-system-prompt"
                value={newAgent.systemPrompt || ''}
                onChange={(e) => setNewAgent(prev => ({ ...prev, systemPrompt: e.target.value }))}
                className="bg-slate-900/50 border-slate-600 text-white placeholder-slate-400"
                placeholder="Define the agent's role, behavior, and expertise..."
              />
            </div>
            
            <div>
              <Label className="text-slate-200">Areas of Expertise</Label>
              <div className="flex gap-2 mt-2">
                <Input
                  value={newExpertise}
                  onChange={(e) => setNewExpertise(e.target.value)}
                  placeholder="Add expertise area..."
                  className="bg-slate-900/50 border-slate-600 text-white flex-1"
                  onKeyPress={(e) => e.key === 'Enter' && handleAddExpertise()}
                />
                <Button onClick={handleAddExpertise} variant="outline" className="border-slate-600">
                  Add
                </Button>
              </div>
              {newAgent.expertise && newAgent.expertise.length > 0 && (
                <div className="mt-3 flex flex-wrap gap-2">
                  {newAgent.expertise.map((skill, index) => (
                    <Badge key={index} variant="secondary" className="bg-slate-700 text-slate-300">
                      {skill}
                      <button
                        onClick={() => handleRemoveExpertise(index)}
                        className="ml-2 text-red-400 hover:text-red-300"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
            
            <div className="flex gap-2">
              <Button onClick={handleAddAgent} className="bg-purple-600 hover:bg-purple-700">
                <Plus className="w-4 h-4 mr-2" />
                Create Agent
              </Button>
              <Button 
                variant="outline" 
                onClick={() => setIsAddingAgent(false)}
                className="border-slate-600 text-slate-300"
              >
                Cancel
              </Button>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};
