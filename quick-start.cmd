@echo off
:: Quick Start - Prompt Studio
:: Double-click this file to quickly start the development server

echo.
echo 💠 Prompt Studio - Quick Start
echo ==============================
echo.

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed
    echo Please install Node.js from https://nodejs.org/
    pause
    exit /b 1
)

:: Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    npm install
)

:: Check for .env file
if not exist ".env" (
    echo 🔧 Creating environment file...
    echo # Add your AI API keys here > .env
    echo VITE_OPENAI_API_KEY=your_openai_api_key_here >> .env
    echo VITE_ANTHROPIC_API_KEY=your_anthropic_api_key_here >> .env
    echo.
    echo ⚠️  Please edit .env file and add your API keys
    echo Opening .env file...
    start notepad .env
    echo Press any key after saving your API keys...
    pause >nul
)

:: Start the development server
echo 🚀 Starting Prompt Studio...
echo Opening http://localhost:5173 in your browser...
echo.
npm run dev
