// AI Service Layer for LLM Integration
export interface AIProvider {
  name: string;
  models: string[];
  maxTokens: number;
}

export interface AIRequest {
  provider: string;
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  provider: string;
  timestamp: Date;
}

export const AI_PROVIDERS: Record<string, AIProvider> = {
  openai: {
    name: 'OpenAI',
    models: ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'],
    maxTokens: 4096
  },
  anthropic: {
    name: 'Anthropic',
    models: ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'],
    maxTokens: 4096
  }
};

class AIService {
  private openaiApiKey: string;
  private anthropicApiKey: string;
  private defaultProvider: string;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;
  private apiTimeout: number;
  private maxRetries: number;

  constructor() {
    this.loadConfiguration();
  }

  private loadConfiguration() {
    // Try to load from localStorage first, then fall back to environment variables
    const savedSettings = this.getSavedSettings();

    this.openaiApiKey = savedSettings?.openaiApiKey || import.meta.env.VITE_OPENAI_API_KEY || '';
    this.anthropicApiKey = savedSettings?.anthropicApiKey || import.meta.env.VITE_ANTHROPIC_API_KEY || '';
    this.defaultProvider = savedSettings?.provider || import.meta.env.VITE_DEFAULT_AI_PROVIDER || 'openai';
    this.defaultModel = savedSettings?.model || import.meta.env.VITE_DEFAULT_MODEL || 'gpt-4';
    this.defaultTemperature = savedSettings?.temperature ?? parseFloat(import.meta.env.VITE_DEFAULT_TEMPERATURE || '0.7');
    this.defaultMaxTokens = savedSettings?.maxTokens ?? parseInt(import.meta.env.VITE_DEFAULT_MAX_TOKENS || '2048');
    this.apiTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');
    this.maxRetries = parseInt(import.meta.env.VITE_MAX_RETRIES || '3');

    // Also check for runtime configuration (set by AIConfiguration component)
    if (typeof window !== 'undefined' && (window as any).__AI_CONFIG__) {
      const runtimeConfig = (window as any).__AI_CONFIG__;
      this.openaiApiKey = runtimeConfig.VITE_OPENAI_API_KEY || this.openaiApiKey;
      this.anthropicApiKey = runtimeConfig.VITE_ANTHROPIC_API_KEY || this.anthropicApiKey;
      this.defaultProvider = runtimeConfig.VITE_DEFAULT_AI_PROVIDER || this.defaultProvider;
      this.defaultModel = runtimeConfig.VITE_DEFAULT_MODEL || this.defaultModel;
      this.defaultTemperature = parseFloat(runtimeConfig.VITE_DEFAULT_TEMPERATURE || this.defaultTemperature.toString());
      this.defaultMaxTokens = parseInt(runtimeConfig.VITE_DEFAULT_MAX_TOKENS || this.defaultMaxTokens.toString());
    }
  }

  private getSavedSettings() {
    if (typeof window === 'undefined') return null;

    try {
      const saved = localStorage.getItem('ai-settings');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  }

  async generateResponse(request: Partial<AIRequest>): Promise<AIResponse> {
    const fullRequest: AIRequest = {
      provider: request.provider || this.defaultProvider,
      model: request.model || this.defaultModel,
      messages: request.messages || [],
      temperature: request.temperature ?? this.defaultTemperature,
      maxTokens: request.maxTokens ?? this.defaultMaxTokens,
      stream: request.stream ?? false
    };

    // Validate API keys
    if (fullRequest.provider === 'openai' && !this.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }
    if (fullRequest.provider === 'anthropic' && !this.anthropicApiKey) {
      throw new Error('Anthropic API key not configured');
    }

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        switch (fullRequest.provider) {
          case 'openai':
            return await this.callOpenAI(fullRequest);
          case 'anthropic':
            return await this.callAnthropic(fullRequest);
          default:
            throw new Error(`Unsupported AI provider: ${fullRequest.provider}`);
        }
      } catch (error) {
        lastError = error as Error;
        if (attempt < this.maxRetries - 1) {
          await this.delay(Math.pow(2, attempt) * 1000); // Exponential backoff
        }
      }
    }

    throw lastError || new Error('Failed to generate AI response');
  }

  private async callOpenAI(request: AIRequest): Promise<AIResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiApiKey}`
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        stream: request.stream
      }),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(`OpenAI API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      },
      model: request.model,
      provider: 'openai',
      timestamp: new Date()
    };
  }

  private async callAnthropic(request: AIRequest): Promise<AIResponse> {
    // Convert OpenAI format to Anthropic format
    const systemMessage = request.messages.find(m => m.role === 'system');
    const userMessages = request.messages.filter(m => m.role !== 'system');

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.anthropicApiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: request.model,
        max_tokens: request.maxTokens,
        temperature: request.temperature,
        system: systemMessage?.content || '',
        messages: userMessages.map(msg => ({
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content: msg.content
        }))
      }),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(`Anthropic API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.content[0]?.text || '',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      },
      model: request.model,
      provider: 'anthropic',
      timestamp: new Date()
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods
  getAvailableProviders(): AIProvider[] {
    return Object.values(AI_PROVIDERS);
  }

  getModelsForProvider(provider: string): string[] {
    return AI_PROVIDERS[provider]?.models || [];
  }

  isConfigured(provider?: string): boolean {
    const targetProvider = provider || this.defaultProvider;
    if (targetProvider === 'openai') return !!this.openaiApiKey;
    if (targetProvider === 'anthropic') return !!this.anthropicApiKey;
    return false;
  }

  getDefaultSettings() {
    return {
      provider: this.defaultProvider,
      model: this.defaultModel,
      temperature: this.defaultTemperature,
      maxTokens: this.defaultMaxTokens
    };
  }

  // Method to refresh configuration (useful when settings are updated)
  refreshConfiguration() {
    this.loadConfiguration();
  }
}

export const aiService = new AIService();
