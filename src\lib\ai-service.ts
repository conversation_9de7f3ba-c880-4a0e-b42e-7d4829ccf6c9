// AI Service Layer for LLM Integration
// Updated June 2025 with latest model listings from all providers
//
// IMPORTANT: Model availability changes frequently. This configuration includes:
// - OpenAI: Latest GPT-4o, O-series reasoning models (o1, o3-mini)
// - Anthropic: <PERSON> 4 (Opus/Sonnet), Claude 3.7 with extended thinking, Claude 3.5
// - Google: Gemini 2.5 (Pro/Flash), Gemini 2.0, Gemini 1.5 stable models
// - Mistral: Magistral series, Mistral Medium/Large, specialized models (Codestral, Devstral)
//
// Note: Some experimental models like "gemini-2.5-pro-exp-1219" may be deprecated.
// Always verify model availability in the respective provider's documentation.
export interface AIProvider {
  name: string;
  models: string[];
  maxTokens: number;
}

export interface AIRequest {
  provider: string;
  model: string;
  messages: Array<{
    role: 'system' | 'user' | 'assistant';
    content: string;
  }>;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

export interface AIResponse {
  content: string;
  usage?: {
    promptTokens: number;
    completionTokens: number;
    totalTokens: number;
  };
  model: string;
  provider: string;
  timestamp: Date;
}

export const AI_PROVIDERS: Record<string, AIProvider> = {
  openai: {
    name: 'OpenAI',
    models: [
      // Latest O-series models (reasoning models)
      'o3-mini-2025-01-31',
      'o1',
      'o1-2024-12-17',
      'o1-mini',
      'o1-preview',
      // Latest GPT-4o models
      'gpt-4o',
      'gpt-4o-2024-11-20',
      'gpt-4o-2024-08-06',
      'gpt-4o-mini',
      'gpt-4o-mini-2024-07-18',
      'chatgpt-4o-latest',
      // Legacy models
      'gpt-4-turbo',
      'gpt-4-turbo-2024-04-09',
      'gpt-3.5-turbo'
    ],
    maxTokens: 4096
  },
  anthropic: {
    name: 'Anthropic',
    models: [
      // Latest Claude 4 models
      'claude-opus-4-20250514',
      'claude-sonnet-4-20250514',
      // Claude 3.7 with extended thinking
      'claude-3-7-sonnet-20250219',
      'claude-3-7-sonnet-latest',
      // Claude 3.5 models
      'claude-3-5-sonnet-20241022',
      'claude-3-5-sonnet-latest',
      'claude-3-5-haiku-20241022',
      'claude-3-5-haiku-latest',
      // Claude 3 models
      'claude-3-opus-20240229',
      'claude-3-opus-latest',
      'claude-3-sonnet-20240229',
      'claude-3-haiku-20240307'
    ],
    maxTokens: 8192
  },
  google: {
    name: 'Google',
    models: [
      // Latest Gemini 2.5 models (current stable)
      'gemini-2.5-pro',
      'gemini-2.5-flash',
      'gemini-2.5-flash-lite-preview-06-17',
      // Gemini 2.0 models
      'gemini-2.0-flash',
      'gemini-2.0-flash-001',
      'gemini-2.0-flash-lite',
      'gemini-2.0-flash-lite-001',
      // Gemini 1.5 models (stable)
      'gemini-1.5-pro',
      'gemini-1.5-pro-latest',
      'gemini-1.5-pro-002',
      'gemini-1.5-flash',
      'gemini-1.5-flash-latest',
      'gemini-1.5-flash-002',
      'gemini-1.5-flash-8b'
    ],
    maxTokens: 8192
  },
  mistral: {
    name: 'Mistral',
    models: [
      // Latest Magistral models (June 2025)
      'magistral-medium-2506',
      'magistral-small-2506',
      // Mistral Medium/Large models
      'mistral-medium-2505',
      'mistral-medium-latest',
      'mistral-large-2411',
      'mistral-large-latest',
      // Mistral Small models
      'mistral-small-2503',
      'mistral-small-latest',
      // Specialized models
      'codestral-2501',
      'codestral-latest',
      'devstral-small-2505',
      'devstral-small-latest',
      // Edge models
      'ministral-8b-2410',
      'ministral-8b-latest',
      'ministral-3b-2410',
      'ministral-3b-latest'
    ],
    maxTokens: 8192
  }
};

class AIService {
  private openaiApiKey: string;
  private anthropicApiKey: string;
  private googleApiKey: string;
  private mistralApiKey: string;
  private defaultProvider: string;
  private defaultModel: string;
  private defaultTemperature: number;
  private defaultMaxTokens: number;
  private apiTimeout: number;
  private maxRetries: number;

  constructor() {
    this.loadConfiguration();
  }

  private loadConfiguration() {
    const savedSettings = this.getSavedSettings();

    // Initialize with ultimate fallback defaults
    let openaiApiKey = '';
    let anthropicApiKey = '';
    let googleApiKey = '';
    let mistralApiKey = '';
    let defaultProvider = 'openai';
    let defaultModel = 'gpt-4o';
    let defaultTemperature = 0.7;
    let defaultMaxTokens = 2048;

    // 1. Try to load from localStorage first
    if (savedSettings) {
      openaiApiKey = savedSettings.openaiApiKey || openaiApiKey;
      anthropicApiKey = savedSettings.anthropicApiKey || anthropicApiKey;
      googleApiKey = savedSettings.googleApiKey || googleApiKey;
      mistralApiKey = savedSettings.mistralApiKey || mistralApiKey;
      defaultProvider = savedSettings.provider || defaultProvider;
      defaultModel = savedSettings.model || defaultModel;
      defaultTemperature = savedSettings.temperature ?? defaultTemperature;
      defaultMaxTokens = savedSettings.maxTokens ?? defaultMaxTokens;
    }

    // 2. Fallback to environment variables if not found in localStorage
    this.openaiApiKey = openaiApiKey || import.meta.env.VITE_OPENAI_API_KEY || '';
    this.anthropicApiKey = anthropicApiKey || import.meta.env.VITE_ANTHROPIC_API_KEY || '';
    this.googleApiKey = googleApiKey || import.meta.env.VITE_GOOGLE_API_KEY || '';
    this.mistralApiKey = mistralApiKey || import.meta.env.VITE_MISTRAL_API_KEY || '';
    this.defaultProvider = defaultProvider || import.meta.env.VITE_DEFAULT_AI_PROVIDER || 'openai';
    this.defaultModel = defaultModel || import.meta.env.VITE_DEFAULT_MODEL || 'gpt-4o';

    // For numeric values, ensure correct fallback order
    if (savedSettings && typeof savedSettings.temperature === 'number') {
      this.defaultTemperature = savedSettings.temperature;
    } else {
      this.defaultTemperature = parseFloat(import.meta.env.VITE_DEFAULT_TEMPERATURE || defaultTemperature.toString());
    }

    if (savedSettings && typeof savedSettings.maxTokens === 'number') {
      this.defaultMaxTokens = savedSettings.maxTokens;
    } else {
      this.defaultMaxTokens = parseInt(import.meta.env.VITE_DEFAULT_MAX_TOKENS || defaultMaxTokens.toString());
    }

    this.apiTimeout = parseInt(import.meta.env.VITE_API_TIMEOUT || '30000');
    this.maxRetries = parseInt(import.meta.env.VITE_MAX_RETRIES || '3');
  }

  private getSavedSettings() {
    if (typeof window === 'undefined') return null;

    try {
      const saved = localStorage.getItem('ai-settings');
      return saved ? JSON.parse(saved) : null;
    } catch {
      return null;
    }
  }

  async generateResponse(request: Partial<AIRequest>): Promise<AIResponse> {
    const fullRequest: AIRequest = {
      provider: request.provider || this.defaultProvider,
      model: request.model || this.defaultModel,
      messages: request.messages || [],
      temperature: request.temperature ?? this.defaultTemperature,
      maxTokens: request.maxTokens ?? this.defaultMaxTokens,
      stream: request.stream ?? false
    };

    // Validate API keys
    if (fullRequest.provider === 'openai' && !this.openaiApiKey) {
      throw new Error('OpenAI API key not configured');
    }
    if (fullRequest.provider === 'anthropic' && !this.anthropicApiKey) {
      throw new Error('Anthropic API key not configured');
    }
    if (fullRequest.provider === 'google' && !this.googleApiKey) {
      throw new Error('Google API key not configured');
    }
    if (fullRequest.provider === 'mistral' && !this.mistralApiKey) {
      throw new Error('Mistral API key not configured');
    }

    let lastError: Error | null = null;
    
    for (let attempt = 0; attempt < this.maxRetries; attempt++) {
      try {
        switch (fullRequest.provider) {
          case 'openai':
            return await this.callOpenAI(fullRequest);
          case 'anthropic':
            return await this.callAnthropic(fullRequest);
          case 'google':
            return await this.callGoogleAPI(fullRequest);
          case 'mistral':
            return await this.callMistralAPI(fullRequest);
          default:
            throw new Error(`Unsupported AI provider: ${fullRequest.provider}`);
        }
      } catch (error) {
        lastError = error as Error;
        if (attempt < this.maxRetries - 1) {
          await this.delay(Math.pow(2, attempt) * 1000);
        }
      }
    }

    throw lastError || new Error('Failed to generate AI response');
  }

  private async callOpenAI(request: AIRequest): Promise<AIResponse> {
    const response = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.openaiApiKey}`
      },
      body: JSON.stringify({
        model: request.model,
        messages: request.messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
        stream: request.stream
      }),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(`OpenAI API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      },
      model: request.model,
      provider: 'openai',
      timestamp: new Date()
    };
  }

  private async callGoogleAPI(request: AIRequest): Promise<AIResponse> {
    const API_ENDPOINT = `https://generativelanguage.googleapis.com/v1beta/models/${request.model}:generateContent?key=${this.googleApiKey}`;

    let systemInstruction: { role: string; parts: { text: string }[] } | undefined;
    const regularMessages = request.messages.filter(msg => {
      if (msg.role === 'system') {
        systemInstruction = { role: 'system', parts: [{ text: msg.content }] };
        return false;
      }
      return true;
    });

    const contents = regularMessages.map(msg => ({
      role: msg.role === 'assistant' ? 'model' : 'user',
      parts: [{ text: msg.content }],
    }));

    const requestBody: any = {
      contents: contents,
      generationConfig: {
        temperature: request.temperature,
        maxOutputTokens: request.maxTokens,
      },
    };

    if (systemInstruction) {
      requestBody.systemInstruction = { parts: systemInstruction.parts };
    }

    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(requestBody),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: 'Unknown Google API error', code: response.status } }));
      throw new Error(`Google API error (${errorData.error?.code || response.status}): ${errorData.error?.message || response.statusText}`);
    }

    const data = await response.json();

    if (data.promptFeedback?.blockReason) {
      throw new Error(`Google API request blocked: ${data.promptFeedback.blockReason}. ${data.promptFeedback.safetyRatings?.map((r: any) => `${r.category}: ${r.probability}`).join(', ') || ''}`);
    }

    const candidate = data.candidates?.[0];
    if (!candidate) {
      throw new Error('Google API error: No candidate response received.');
    }

    if (candidate.finishReason && candidate.finishReason !== 'STOP' && candidate.finishReason !== 'MAX_TOKENS') {
      throw new Error(`Google API error: Unfinished response. Finish reason: ${candidate.finishReason}. ${candidate.safetyRatings?.map((r: any) => `${r.category}: ${r.probability}`).join(', ') || ''}`);
    }

    const content = candidate.content?.parts?.[0]?.text || '';

    const usage = {
      promptTokens: data.usageMetadata?.promptTokenCount || 0,
      completionTokens: data.usageMetadata?.candidatesTokenCount || 0,
      totalTokens: data.usageMetadata?.totalTokenCount || 0,
    };

    return {
      content,
      usage,
      model: request.model,
      provider: 'google',
      timestamp: new Date()
    };
  }

  private async callAnthropic(request: AIRequest): Promise<AIResponse> {
    const systemMessage = request.messages.find(m => m.role === 'system');
    const userMessages = request.messages.filter(m => m.role !== 'system');

    const response = await fetch('https://api.anthropic.com/v1/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': this.anthropicApiKey,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify({
        model: request.model,
        max_tokens: request.maxTokens,
        temperature: request.temperature,
        system: systemMessage?.content || '',
        messages: userMessages.map(msg => ({
          role: msg.role === 'assistant' ? 'assistant' : 'user',
          content: msg.content
        }))
      }),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const error = await response.json().catch(() => ({ error: { message: 'Unknown error' } }));
      throw new Error(`Anthropic API error: ${error.error?.message || response.statusText}`);
    }

    const data = await response.json();
    
    return {
      content: data.content[0]?.text || '',
      usage: {
        promptTokens: data.usage?.input_tokens || 0,
        completionTokens: data.usage?.output_tokens || 0,
        totalTokens: (data.usage?.input_tokens || 0) + (data.usage?.output_tokens || 0)
      },
      model: request.model,
      provider: 'anthropic',
      timestamp: new Date()
    };
  }

  private async callMistralAPI(request: AIRequest): Promise<AIResponse> {
    const API_ENDPOINT = 'https://api.mistral.ai/v1/chat/completions';

    let messages = request.messages;
    const systemMessageIndex = messages.findIndex(msg => msg.role === 'system');
    if (systemMessageIndex > 0) {
      const systemMsg = messages.splice(systemMessageIndex, 1)[0];
      messages = [systemMsg, ...messages];
    }

    const response = await fetch(API_ENDPOINT, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.mistralApiKey}`
      },
      body: JSON.stringify({
        model: request.model,
        messages: messages,
        temperature: request.temperature,
        max_tokens: request.maxTokens,
      }),
      signal: AbortSignal.timeout(this.apiTimeout)
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({ error: { message: 'Unknown Mistral API error', code: response.status } }));
      const message = errorData.message || errorData.detail || errorData.error?.message || response.statusText;
      const code = errorData.code || errorData.error?.code || response.status;
      throw new Error(`Mistral API error (${code}): ${message}`);
    }

    const data = await response.json();

    return {
      content: data.choices[0]?.message?.content || '',
      usage: {
        promptTokens: data.usage?.prompt_tokens || 0,
        completionTokens: data.usage?.completion_tokens || 0,
        totalTokens: data.usage?.total_tokens || 0
      },
      model: request.model,
      provider: 'mistral',
      timestamp: new Date()
    };
  }

  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Utility methods
  getAvailableProviders(): AIProvider[] {
    return Object.values(AI_PROVIDERS);
  }

  getModelsForProvider(provider: string): string[] {
    return AI_PROVIDERS[provider]?.models || [];
  }

  isConfigured(provider?: string): boolean {
    const targetProvider = provider || this.defaultProvider;
    if (targetProvider === 'openai') return !!this.openaiApiKey;
    if (targetProvider === 'anthropic') return !!this.anthropicApiKey;
    if (targetProvider === 'google') return !!this.googleApiKey;
    if (targetProvider === 'mistral') return !!this.mistralApiKey;
    return false;
  }

  getDefaultSettings() {
    return {
      provider: this.defaultProvider,
      model: this.defaultModel,
      temperature: this.defaultTemperature,
      maxTokens: this.defaultMaxTokens
    };
  }

  refreshConfiguration() {
    this.loadConfiguration();
  }
}

export const aiService = new AIService();
