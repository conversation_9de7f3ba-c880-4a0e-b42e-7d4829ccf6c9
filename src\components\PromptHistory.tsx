
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Search, GitBranch, Archive, Star } from 'lucide-react';
import { useState } from 'react';

export const PromptHistory = () => {
  const [searchTerm, setSearchTerm] = useState('');

  // Mock history data
  const historyItems = [
    {
      id: 1,
      title: "Creative Writing Assistant",
      prompt: "You are a creative writing assistant that helps users develop compelling narratives...",
      version: "v2.1",
      status: "greenlit",
      lastModified: "2024-01-15",
      models: ["GPT-4", "Claude"],
      averageScore: 89
    },
    {
      id: 2,
      title: "Code Review Bot",
      prompt: "Review the following code and provide constructive feedback...",
      version: "v1.5",
      status: "testing",
      lastModified: "2024-01-14",
      models: ["GPT-4", "Gemini"],
      averageScore: 76
    },
    {
      id: 3,
      title: "Data Analysis Helper",
      prompt: "Analyze the following dataset and provide insights...",
      version: "v3.0",
      status: "archived",
      lastModified: "2024-01-12",
      models: ["<PERSON>", "Mistral"],
      averageScore: 92
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'greenlit': return 'bg-green-600';
      case 'testing': return 'bg-yellow-600';
      case 'archived': return 'bg-slate-600';
      default: return 'bg-slate-600';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'greenlit': return <Star className="w-3 h-3" />;
      case 'testing': return <GitBranch className="w-3 h-3" />;
      case 'archived': return <Archive className="w-3 h-3" />;
      default: return null;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-bold text-slate-200">Prompt History</h2>
        <div className="relative w-64">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 w-4 h-4" />
          <Input
            placeholder="Search prompts..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-600 text-white"
          />
        </div>
      </div>

      <div className="grid gap-4">
        {historyItems.map((item) => (
          <Card key={item.id} className="bg-slate-800/50 border-slate-700 p-6">
            <div className="flex items-start justify-between mb-4">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-lg font-semibold text-slate-200">{item.title}</h3>
                  <Badge className={`${getStatusColor(item.status)} text-white flex items-center gap-1`}>
                    {getStatusIcon(item.status)}
                    {item.status}
                  </Badge>
                  <Badge variant="outline" className="border-slate-500 text-slate-300">
                    {item.version}
                  </Badge>
                </div>
                <p className="text-slate-400 text-sm mb-3">
                  {item.prompt.slice(0, 150)}...
                </p>
                <div className="flex items-center gap-4 text-sm text-slate-400">
                  <span>Modified: {item.lastModified}</span>
                  <span>Models: {item.models.join(', ')}</span>
                  <span>Avg Score: {item.averageScore}%</span>
                </div>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                  Fork
                </Button>
                <Button variant="outline" size="sm" className="border-slate-600 text-slate-300">
                  View
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
};
