import { aiService } from './ai-service';

export interface ScoringArchetype {
  id: string;
  name: string;
  description: string;
  toneKeywords: string[];
  structurePatterns: RegExp[];
  qualityIndicators: string[];
  expectedLength: { min: number; max: number };
  formalityLevel: 'casual' | 'professional' | 'academic' | 'technical';
}

export interface SandboxMetrics {
  responseTime: number;
  tokenUsage: number;
  errorRate: number;
  consistencyScore: number;
  contextAdherence: number;
}

export interface AutoScoreResult {
  fidelity: number;
  adherence: number;
  consistency: number;
  creativity: number;
  accuracy: number;
  toneMatch: number;
  structureScore: number;
  overallScore: number;
  breakdown: {
    archetypeMatch: number;
    sandboxScore: number;
    contentQuality: number;
    technicalAccuracy: number;
  };
  recommendations: string[];
  confidence: number;
}

export interface ScoringRequest {
  response: string;
  prompt: string;
  systemPrompt?: string;
  expectedArchetype?: string;
  sandboxMetrics?: SandboxMetrics;
  context?: {
    domain: string;
    audience: string;
    purpose: string;
  };
}

// Predefined archetypes for different types of responses
export const SCORING_ARCHETYPES: Record<string, ScoringArchetype> = {
  technical_documentation: {
    id: 'technical_documentation',
    name: 'Technical Documentation',
    description: 'Clear, structured technical explanations with examples',
    toneKeywords: ['implement', 'configure', 'execute', 'function', 'method', 'parameter'],
    structurePatterns: [/^\d+\./m, /```[\s\S]*?```/m, /^#{1,6}\s/m],
    qualityIndicators: ['example', 'code', 'step', 'note', 'warning'],
    expectedLength: { min: 200, max: 2000 },
    formalityLevel: 'technical'
  },
  creative_writing: {
    id: 'creative_writing',
    name: 'Creative Writing',
    description: 'Imaginative, engaging content with narrative elements',
    toneKeywords: ['imagine', 'story', 'character', 'scene', 'emotion', 'vivid'],
    structurePatterns: [/[.!?]\s*$/m, /["'].*?["']/m],
    qualityIndicators: ['metaphor', 'imagery', 'dialogue', 'setting', 'plot'],
    expectedLength: { min: 150, max: 1500 },
    formalityLevel: 'casual'
  },
  business_analysis: {
    id: 'business_analysis',
    name: 'Business Analysis',
    description: 'Data-driven insights with actionable recommendations',
    toneKeywords: ['analyze', 'recommend', 'strategy', 'market', 'revenue', 'growth'],
    structurePatterns: [/^\d+\./m, /^[A-Z][^.]*:$/m, /\d+%/m],
    qualityIndicators: ['data', 'metric', 'trend', 'recommendation', 'impact'],
    expectedLength: { min: 300, max: 1200 },
    formalityLevel: 'professional'
  },
  academic_research: {
    id: 'academic_research',
    name: 'Academic Research',
    description: 'Scholarly analysis with citations and methodology',
    toneKeywords: ['research', 'study', 'analysis', 'methodology', 'findings', 'conclusion'],
    structurePatterns: [/\([^)]*\d{4}[^)]*\)/m, /^Abstract:?/mi, /^Introduction:?/mi],
    qualityIndicators: ['hypothesis', 'methodology', 'results', 'discussion', 'references'],
    expectedLength: { min: 500, max: 3000 },
    formalityLevel: 'academic'
  },
  customer_support: {
    id: 'customer_support',
    name: 'Customer Support',
    description: 'Helpful, empathetic responses to customer inquiries',
    toneKeywords: ['help', 'understand', 'sorry', 'resolve', 'assist', 'support'],
    structurePatterns: [/^Thank you/mi, /^I understand/mi, /^Let me help/mi],
    qualityIndicators: ['empathy', 'solution', 'steps', 'follow-up', 'contact'],
    expectedLength: { min: 100, max: 800 },
    formalityLevel: 'professional'
  }
};

class AutoScoringService {
  async scoreResponse(request: ScoringRequest): Promise<AutoScoreResult> {
    const archetype = request.expectedArchetype ? 
      SCORING_ARCHETYPES[request.expectedArchetype] : 
      this.detectArchetype(request.response, request.prompt);

    // Calculate individual scores
    const fidelity = this.calculateFidelity(request.response, request.prompt);
    const adherence = this.calculateAdherence(request.response, request.systemPrompt);
    const consistency = this.calculateConsistency(request.response);
    const creativity = this.calculateCreativity(request.response);
    const accuracy = await this.calculateAccuracy(request.response, request.prompt);
    const toneMatch = this.calculateToneMatch(request.response, archetype);
    const structureScore = this.calculateStructureScore(request.response, archetype);

    // Calculate breakdown scores
    const archetypeMatch = this.calculateArchetypeMatch(request.response, archetype);
    const sandboxScore = this.calculateSandboxScore(request.sandboxMetrics);
    const contentQuality = (fidelity + consistency + creativity) / 3;
    const technicalAccuracy = accuracy;

    // Calculate overall score with weights
    const overallScore = this.calculateWeightedScore({
      fidelity,
      adherence,
      consistency,
      creativity,
      accuracy,
      toneMatch,
      structureScore
    });

    // Generate recommendations
    const recommendations = this.generateRecommendations({
      fidelity,
      adherence,
      consistency,
      creativity,
      accuracy,
      toneMatch,
      structureScore,
      archetype,
      response: request.response
    });

    // Calculate confidence based on score consistency
    const confidence = this.calculateConfidence([
      fidelity, adherence, consistency, creativity, accuracy, toneMatch, structureScore
    ]);

    return {
      fidelity,
      adherence,
      consistency,
      creativity,
      accuracy,
      toneMatch,
      structureScore,
      overallScore,
      breakdown: {
        archetypeMatch,
        sandboxScore,
        contentQuality,
        technicalAccuracy
      },
      recommendations,
      confidence
    };
  }

  private detectArchetype(response: string, prompt: string): ScoringArchetype {
    const combinedText = `${prompt} ${response}`.toLowerCase();
    let bestMatch = SCORING_ARCHETYPES.technical_documentation;
    let bestScore = 0;

    for (const archetype of Object.values(SCORING_ARCHETYPES)) {
      let score = 0;
      
      // Check for tone keywords
      for (const keyword of archetype.toneKeywords) {
        if (combinedText.includes(keyword.toLowerCase())) {
          score += 1;
        }
      }
      
      // Check for structure patterns
      for (const pattern of archetype.structurePatterns) {
        if (pattern.test(response)) {
          score += 2;
        }
      }
      
      // Check for quality indicators
      for (const indicator of archetype.qualityIndicators) {
        if (combinedText.includes(indicator.toLowerCase())) {
          score += 1;
        }
      }

      if (score > bestScore) {
        bestScore = score;
        bestMatch = archetype;
      }
    }

    return bestMatch;
  }

  private calculateFidelity(response: string, prompt: string): number {
    let score = 50; // Base score

    // Length appropriateness
    if (response.length > 50 && response.length < 2000) score += 15;
    else if (response.length > 20) score += 10;

    // Relevance to prompt
    const promptWords = prompt.toLowerCase().split(/\s+/).filter(w => w.length > 3);
    const responseWords = response.toLowerCase().split(/\s+/);
    const relevantWords = promptWords.filter(word => responseWords.includes(word));
    score += Math.min(20, (relevantWords.length / promptWords.length) * 20);

    // Completeness indicators
    if (response.includes('\n') || response.match(/\d+\./)) score += 10;
    if (response.endsWith('.') || response.endsWith('!') || response.endsWith('?')) score += 5;

    return Math.min(100, Math.max(0, score));
  }

  private calculateAdherence(response: string, systemPrompt?: string): number {
    if (!systemPrompt) return 75; // Default when no system prompt

    let score = 50;

    // Format adherence
    if (systemPrompt.toLowerCase().includes('format') || systemPrompt.toLowerCase().includes('structure')) {
      if (response.includes('\n') || response.match(/\d+\./) || response.includes('•')) {
        score += 20;
      }
    }

    // Tone adherence
    const toneWords = ['professional', 'casual', 'formal', 'friendly', 'technical'];
    const systemTone = toneWords.find(tone => systemPrompt.toLowerCase().includes(tone));
    if (systemTone) {
      const responseTone = this.detectResponseTone(response);
      if (responseTone === systemTone) score += 15;
    }

    // Instruction following
    if (systemPrompt.toLowerCase().includes('list') && response.match(/\d+\.|•|\-/)) score += 15;
    if (systemPrompt.toLowerCase().includes('explain') && response.length > 100) score += 10;

    return Math.min(100, Math.max(0, score));
  }

  private calculateConsistency(response: string): number {
    let score = 60;

    // Check for contradictions
    const sentences = response.split(/[.!?]+/).filter(s => s.trim().length > 10);
    if (sentences.length > 1) {
      const contradictoryPairs = [
        ['yes', 'no'], ['good', 'bad'], ['always', 'never'],
        ['increase', 'decrease'], ['positive', 'negative']
      ];

      let contradictions = 0;
      for (const [word1, word2] of contradictoryPairs) {
        if (response.toLowerCase().includes(word1) && response.toLowerCase().includes(word2)) {
          contradictions++;
        }
      }
      score -= contradictions * 10;
    }

    // Coherence indicators
    const coherenceWords = ['therefore', 'however', 'furthermore', 'additionally', 'consequently'];
    const coherenceCount = coherenceWords.filter(word => 
      response.toLowerCase().includes(word)
    ).length;
    score += Math.min(15, coherenceCount * 5);

    return Math.min(100, Math.max(0, score));
  }

  private calculateCreativity(response: string): number {
    let score = 50;

    // Vocabulary diversity
    const words = response.toLowerCase().split(/\s+/);
    const uniqueWords = new Set(words);
    const diversity = uniqueWords.size / words.length;
    score += diversity * 20;

    // Creative elements
    if (response.toLowerCase().includes('example') || response.toLowerCase().includes('like')) {
      score += 10;
    }

    // Creative language patterns
    const creativePatterns = [
      /metaphor|analogy|imagine|picture|envision/i,
      /innovative|creative|unique|novel|original/i
    ];

    for (const pattern of creativePatterns) {
      if (pattern.test(response)) score += 5;
    }

    return Math.min(100, Math.max(0, score));
  }

  private async calculateAccuracy(response: string, prompt: string): Promise<number> {
    try {
      // Use AI to assess factual accuracy
      const assessmentPrompt = `Assess the factual accuracy of this response to the given prompt. Rate from 0-100.

Prompt: ${prompt}
Response: ${response}

Consider:
1. Factual correctness
2. Logical consistency
3. Technical accuracy (if applicable)
4. Absence of misinformation

Provide only a number between 0-100.`;

      const aiResponse = await aiService.generateResponse({
        messages: [
          { role: 'system', content: 'You are an expert fact-checker. Provide only numerical accuracy scores.' },
          { role: 'user', content: assessmentPrompt }
        ],
        temperature: 0.1,
        maxTokens: 50
      });

      const score = parseInt(aiResponse.content.match(/\d+/)?.[0] || '75');
      return Math.min(100, Math.max(0, score));
    } catch (error) {
      console.error('Failed to calculate accuracy:', error);
      return 75; // Default score
    }
  }

  private calculateToneMatch(response: string, archetype: ScoringArchetype): number {
    let score = 50;

    // Check for archetype-specific tone keywords
    const responseText = response.toLowerCase();
    const matchedKeywords = archetype.toneKeywords.filter(keyword => 
      responseText.includes(keyword.toLowerCase())
    );
    score += Math.min(30, (matchedKeywords.length / archetype.toneKeywords.length) * 30);

    // Formality level assessment
    const formalityScore = this.assessFormality(response);
    const expectedFormality = this.getFormalityScore(archetype.formalityLevel);
    const formalityDiff = Math.abs(formalityScore - expectedFormality);
    score += Math.max(0, 20 - formalityDiff);

    return Math.min(100, Math.max(0, score));
  }

  private calculateStructureScore(response: string, archetype: ScoringArchetype): number {
    let score = 50;

    // Check for expected structure patterns
    const matchedPatterns = archetype.structurePatterns.filter(pattern => 
      pattern.test(response)
    );
    score += Math.min(25, (matchedPatterns.length / archetype.structurePatterns.length) * 25);

    // Length appropriateness
    const length = response.length;
    if (length >= archetype.expectedLength.min && length <= archetype.expectedLength.max) {
      score += 15;
    } else {
      const deviation = Math.min(
        Math.abs(length - archetype.expectedLength.min),
        Math.abs(length - archetype.expectedLength.max)
      );
      score += Math.max(0, 15 - (deviation / 100));
    }

    // Quality indicators
    const responseText = response.toLowerCase();
    const matchedIndicators = archetype.qualityIndicators.filter(indicator => 
      responseText.includes(indicator.toLowerCase())
    );
    score += Math.min(10, (matchedIndicators.length / archetype.qualityIndicators.length) * 10);

    return Math.min(100, Math.max(0, score));
  }

  private calculateArchetypeMatch(response: string, archetype: ScoringArchetype): number {
    const toneMatch = this.calculateToneMatch(response, archetype);
    const structureScore = this.calculateStructureScore(response, archetype);
    return (toneMatch + structureScore) / 2;
  }

  private calculateSandboxScore(metrics?: SandboxMetrics): number {
    if (!metrics) return 75; // Default score

    let score = 0;

    // Response time score (lower is better)
    if (metrics.responseTime < 1000) score += 25;
    else if (metrics.responseTime < 3000) score += 20;
    else if (metrics.responseTime < 5000) score += 15;
    else score += 10;

    // Token efficiency score
    if (metrics.tokenUsage < 500) score += 25;
    else if (metrics.tokenUsage < 1000) score += 20;
    else if (metrics.tokenUsage < 2000) score += 15;
    else score += 10;

    // Error rate score (lower is better)
    score += Math.max(0, 25 - (metrics.errorRate * 25));

    // Consistency and context scores
    score += (metrics.consistencyScore / 100) * 12.5;
    score += (metrics.contextAdherence / 100) * 12.5;

    return Math.min(100, Math.max(0, score));
  }

  private calculateWeightedScore(scores: {
    fidelity: number;
    adherence: number;
    consistency: number;
    creativity: number;
    accuracy: number;
    toneMatch: number;
    structureScore: number;
  }): number {
    const weights = {
      fidelity: 0.2,
      adherence: 0.15,
      consistency: 0.15,
      creativity: 0.1,
      accuracy: 0.2,
      toneMatch: 0.1,
      structureScore: 0.1
    };

    return Object.entries(scores).reduce((total, [key, score]) => {
      return total + (score * weights[key as keyof typeof weights]);
    }, 0);
  }

  private generateRecommendations(data: {
    fidelity: number;
    adherence: number;
    consistency: number;
    creativity: number;
    accuracy: number;
    toneMatch: number;
    structureScore: number;
    archetype: ScoringArchetype;
    response: string;
  }): string[] {
    const recommendations: string[] = [];

    if (data.fidelity < 70) {
      recommendations.push('Improve response relevance to the original prompt');
    }

    if (data.adherence < 70) {
      recommendations.push('Better follow the system prompt instructions');
    }

    if (data.consistency < 70) {
      recommendations.push('Ensure internal consistency and avoid contradictions');
    }

    if (data.creativity < 60) {
      recommendations.push('Add more creative elements and varied vocabulary');
    }

    if (data.accuracy < 80) {
      recommendations.push('Verify factual accuracy and technical correctness');
    }

    if (data.toneMatch < 70) {
      recommendations.push(`Adjust tone to better match ${data.archetype.name} style`);
    }

    if (data.structureScore < 70) {
      recommendations.push('Improve response structure and formatting');
    }

    if (recommendations.length === 0) {
      recommendations.push('Excellent response quality - maintain current approach');
    }

    return recommendations;
  }

  private calculateConfidence(scores: number[]): number {
    const mean = scores.reduce((sum, score) => sum + score, 0) / scores.length;
    const variance = scores.reduce((sum, score) => sum + Math.pow(score - mean, 2), 0) / scores.length;
    const standardDeviation = Math.sqrt(variance);
    
    // Lower standard deviation = higher confidence
    const confidence = Math.max(0, 100 - (standardDeviation * 2));
    return Math.min(100, confidence);
  }

  private detectResponseTone(response: string): string {
    const lowerResponse = response.toLowerCase();
    
    if (lowerResponse.includes('please') || lowerResponse.includes('thank you')) {
      return 'professional';
    } else if (lowerResponse.includes('hey') || lowerResponse.includes('awesome')) {
      return 'casual';
    } else if (lowerResponse.includes('furthermore') || lowerResponse.includes('therefore')) {
      return 'formal';
    } else if (lowerResponse.includes('!') || lowerResponse.includes('great')) {
      return 'friendly';
    } else {
      return 'technical';
    }
  }

  private assessFormality(response: string): number {
    let formalityScore = 50;

    // Formal indicators
    const formalWords = ['furthermore', 'therefore', 'consequently', 'moreover', 'nevertheless'];
    const formalCount = formalWords.filter(word => 
      response.toLowerCase().includes(word)
    ).length;
    formalityScore += formalCount * 10;

    // Casual indicators
    const casualWords = ['hey', 'awesome', 'cool', 'yeah', 'gonna'];
    const casualCount = casualWords.filter(word => 
      response.toLowerCase().includes(word)
    ).length;
    formalityScore -= casualCount * 10;

    return Math.min(100, Math.max(0, formalityScore));
  }

  private getFormalityScore(level: string): number {
    switch (level) {
      case 'casual': return 25;
      case 'professional': return 50;
      case 'academic': return 75;
      case 'technical': return 60;
      default: return 50;
    }
  }
}

export const autoScoringService = new AutoScoringService();
