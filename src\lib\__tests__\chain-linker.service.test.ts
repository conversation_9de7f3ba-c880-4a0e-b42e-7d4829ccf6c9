import { vi, describe, it, expect, beforeEach } from 'vitest';
import { chainLinkerService, ChainNode, ChainFlow, ExecutionContext } from '../chain-linker';
import { aiService } from '../ai-service'; // To be mocked
import { agentSimulationService } from '../agent-simulation'; // To be mocked
import { Agent } from '@/store/promptStore';


// Mock aiService
vi.mock('../ai-service', () => ({
  aiService: {
    generateResponse: vi.fn(),
    getDefaultSettings: vi.fn(() => ({
        provider: 'openai', model: 'gpt-4', temperature: 0.7, maxTokens: 1024
    }))
  }
}));

// Mock agentSimulationService
vi.mock('../agent-simulation', () => ({
  agentSimulationService: {
    runSimulation: vi.fn()
  }
}));

describe('ChainLinkerService', () => {
  let service: typeof chainLinkerService;
  let sampleFlow: ChainFlow;

  beforeEach(() => {
    service = chainLinkerService; // Use the actual instance
    vi.clearAllMocks();

    // Create a basic flow for testing
    sampleFlow = service.createFlow('Test Flow', 'A flow for testing');
  });

  describe('Node Execution', () => {
    describe('executePromptNode', () => {
      let promptNode: ChainNode;
      const mockAiResponse = {
        content: 'Mocked AI Response',
        model: 'gpt-4-test',
        provider: 'openai',
        usage: { promptTokens: 10, completionTokens: 20, totalTokens: 30 },
        timestamp: new Date(),
      };

      beforeEach(() => {
        promptNode = service.addNode(sampleFlow, 'prompt', { x: 0, y: 0 });
        promptNode.data.content = 'User prompt: {{input}} with variable {{var1}}.';
        promptNode.data.config = {
          provider: 'openai',
          model: 'gpt-test',
          temperature: 0.5,
          maxTokens: 500,
          systemPrompt: 'System prompt: {{sysVar}}.',
        };
        (aiService.generateResponse as vi.Mock).mockResolvedValue(mockAiResponse);
      });

      it('should call aiService.generateResponse with correct parameters', async () => {
        const inputs = { input: 'Test Input', variables: { var1: 'VAR1Value', sysVar: 'SYSVARValue' } };
        // Directly call executePromptNode (it's private, so test via executeNode or make it temporarily public/testable)
        // For this test, let's assume we can test it by executing the node in a flow.
        sampleFlow.nodes = [promptNode]; // Ensure node is in flow
        const context: ExecutionContext = { variables: {}, results: {}, executionPath: [] };
        await service['executeNode'](sampleFlow, promptNode.id, context); // Accessing private for test

        expect(aiService.generateResponse).toHaveBeenCalledWith(
          expect.objectContaining({
            provider: 'openai',
            model: 'gpt-test',
            messages: [
              { role: 'system', content: 'System prompt: SYSVARValue.' },
              { role: 'user', content: 'User prompt: Test Input with variable VAR1Value.' },
            ],
            temperature: 0.5,
            maxTokens: 500,
          })
        );
      });

      it('should map aiService response to node output', async () => {
        const inputs = { input: 'Test Input', variables: { var1: 'VAR1Value', sysVar: 'SYSVARValue' } };
        sampleFlow.nodes = [promptNode];
        const context: ExecutionContext = { variables: {}, results: {}, executionPath: [] };
        await service['executeNode'](sampleFlow, promptNode.id, context);

        const nodeResult = context.results[promptNode.id];
        expect(nodeResult.response).toBe('Mocked AI Response');
        expect(nodeResult.metadata.model).toBe(mockAiResponse.model);
        expect(nodeResult.metadata.provider).toBe(mockAiResponse.provider);
        expect(nodeResult.metadata.tokens).toBe(mockAiResponse.usage.totalTokens);
        expect(nodeResult.metadata.duration).toBeTypeOf('number');
      });

      it('should handle missing optional inputs like variables or systemPrompt', async () => {
        promptNode.data.config.systemPrompt = undefined;
        promptNode.data.content = 'User prompt without variables.';
        const inputs = { input: 'Test Input Only' }; // No variables object

        sampleFlow.nodes = [promptNode];
        const context: ExecutionContext = { variables: {}, results: {}, executionPath: [] };
        await service['executeNode'](sampleFlow, promptNode.id, context);

         expect(aiService.generateResponse).toHaveBeenCalledWith(
          expect.objectContaining({
            messages: [
              // No system message expected
              { role: 'user', content: 'User prompt without variables.' },
            ],
          })
        );
      });
    });

    describe('executeAgentNode', () => {
      let agentNode: ChainNode;
      const mockAgentSimResponse = { // Based on AgentSimulationResult structure
        agentResponses: [{
            response: 'Mocked Agent Response',
            confidence: 0.85,
            keyInsights: ['Insight 1'],
            agentId: 'default-agent',
            agentName: 'Test Agent',
            aiResponse: { content: 'Mocked Agent Response', model: 'gpt-agent', provider: 'openai', timestamp: new Date(), usage: {totalTokens: 50} }
        }],
        scenario: 'Processed scenario',
        summary: { consensus: '', conflicts: [], recommendations: [] },
        timestamp: new Date(),
        metadata: { duration: 100, totalTokens: 50, averageConfidence: 0.85 }
      };

      beforeEach(() => {
        agentNode = service.addNode(sampleFlow, 'agent', {x:0, y:0});
        agentNode.data.config = {
            agentId: 'test-agent-id',
            agentName: 'Configured Test Agent',
            agentRole: 'Tester',
            agentSystemPrompt: 'You are a test agent: {{var}}',
            agentPersonality: 'Testy',
            agentExpertise: ['testing', 'mocking'],
            provider: 'openai', // For the agent's LLM
            model: 'gpt-agent-model',
            temperature: 0.6,
            maxTokens: 300
        };
        (agentSimulationService.runSimulation as vi.Mock).mockResolvedValue(mockAgentSimResponse);
      });

      it('should call agentSimulationService.runSimulation with correct parameters', async () => {
        const inputs = { scenario: 'Test Scenario', variables: { var: 'variable value' } };
        sampleFlow.nodes = [agentNode];
        const context: ExecutionContext = { variables: {}, results: {}, executionPath: [] };
        await service['executeNode'](sampleFlow, agentNode.id, context);

        expect(agentSimulationService.runSimulation).toHaveBeenCalledWith(
          expect.objectContaining({
            agents: expect.arrayContaining([
              expect.objectContaining({
                id: 'test-agent-id',
                name: 'Configured Test Agent',
                role: 'Tester',
                systemPrompt: 'You are a test agent: variable value', // Variable processed
                personality: 'Testy',
                expertise: ['testing', 'mocking'],
                modelConfig: expect.objectContaining({
                    provider: 'openai',
                    model: 'gpt-agent-model',
                    temperature: 0.6,
                    maxTokens: 300
                })
              })
            ]),
            scenario: 'Test Scenario', // Original scenario, variables applied if any {{}} in it
            temperature: 0.6, // Passed through
            maxTokens: 300    // Passed through
          })
        );
      });

      it('should map agentSimulationService response to node output', async () => {
        const inputs = { scenario: 'Test Scenario' };
        sampleFlow.nodes = [agentNode];
        const context: ExecutionContext = { variables: {}, results: {}, executionPath: [] };
        await service['executeNode'](sampleFlow, agentNode.id, context);

        const nodeResult = context.results[agentNode.id];
        expect(nodeResult.response).toBe('Mocked Agent Response');
        expect(nodeResult.confidence).toBe(0.85);
        expect(nodeResult.insights).toEqual(['Insight 1']);
        expect(nodeResult.metadata.agentName).toBe('Test Agent'); // From the mock response's agent
        expect(nodeResult.metadata.totalTokens).toBe(50);
      });
    });
  });

  describe('Flow Logic and Validation', () => {
    let node1: ChainNode, node2: ChainNode, node3: ChainNode;

    beforeEach(() => {
        node1 = service.addNode(sampleFlow, 'input', { x: 0, y: 0 });
        node1.outputs = [{ id: 'out1', label: 'Out', type: 'string' }];
        node2 = service.addNode(sampleFlow, 'prompt', { x: 200, y: 0 });
        node2.inputs = [{ id: 'in1', label: 'In', type: 'string', required: true }];
        node2.outputs = [{ id: 'out1', label: 'Out', type: 'string' }];
        node3 = service.addNode(sampleFlow, 'output', { x: 400, y: 0 });
        node3.inputs = [{ id: 'in1', label: 'In', type: 'string', required: true }];
    });

    describe('addConnection', () => {
      it('should add a valid connection', () => {
        service.addConnection(sampleFlow, node1.id, 'out1', node2.id, 'in1');
        expect(sampleFlow.connections.length).toBe(1);
        expect(sampleFlow.connections[0].sourceNodeId).toBe(node1.id);
      });

      it('should throw an error when attempting to create a direct cycle', () => {
        service.addConnection(sampleFlow, node1.id, 'out1', node2.id, 'in1');
        // Attempt to connect node2 back to node1 (or node1 to itself if it had input)
        // For this test, let's assume node2 has an output and node1 has an input
        node1.inputs = [{id: 'in_n1', label:'In N1', type: 'string', required: false}];
        node2.outputs = [{id: 'out_n2', label:'Out N2', type: 'string'}];
        sampleFlow.nodes = [node1, node2]; // Update flow with modified nodes

        expect(() => service.addConnection(sampleFlow, node2.id, 'out_n2', node1.id, 'in_n1'))
          .toThrow('Connection would create a cycle');
      });
       it('should throw an error when attempting to create an indirect cycle', () => {
        service.addConnection(sampleFlow, node1.id, 'out1', node2.id, 'in1');
        service.addConnection(sampleFlow, node2.id, 'out1', node3.id, 'in1');
        // Now try to connect node3 back to node1 (needs node3 output, node1 input)
        node3.outputs = [{id: 'out_n3', label:'Out N3', type: 'string'}];
        node1.inputs = [{id: 'in_n1', label:'In N1', type: 'string', required: false}];
        sampleFlow.nodes = [node1, node2, node3]; // Update flow with modified nodes

        expect(() => service.addConnection(sampleFlow, node3.id, 'out_n3', node1.id, 'in_n1'))
          .toThrow('Connection would create a cycle');
      });
    });

    describe('validateFlow', () => {
      it('should identify a flow with cycles as invalid', () => {
        service.addConnection(sampleFlow, node1.id, 'out1', node2.id, 'in1');
        node1.inputs = [{id: 'in_n1', label:'In N1', type: 'string', required: false}];
        node2.outputs = [{id: 'out_n2', label:'Out N2', type: 'string'}];
        sampleFlow.nodes = [node1, node2];
        // Create the cycle by adding the connection directly to the array for test setup
        sampleFlow.connections.push({ id: 'cycle_conn', sourceNodeId: node2.id, sourceOutputId: 'out_n2', targetNodeId: node1.id, targetInputId: 'in_n1'});

        const validation = service.validateFlow(sampleFlow);
        expect(validation.valid).toBe(false);
        expect(validation.errors).toContain('Flow contains cycles');
      });

      it('should identify a flow with orphaned nodes as invalid', () => {
        // node3 is orphaned as it's not connected yet
        const validation = service.validateFlow(sampleFlow);
        expect(validation.valid).toBe(false);
        expect(validation.errors.some(e => e.includes('Orphaned nodes found'))).toBe(true);
      });

      it('should identify missing required inputs', () => {
        // node2 has a required input 'in1', but we haven't connected it yet.
        // node1 (input node) and node3 (output node) are exempt from orphaned check in validateFlow.
        // Let's connect node2 to node3, leaving node2's input unconnected.
        const nodeP = service.addNode(sampleFlow, 'prompt', {x:0,y:0}); // nodeP is node.id = 4
        const nodeO = service.addNode(sampleFlow, 'output', {x:0,y:0}); // nodeO is node.id = 5
        nodeP.inputs = [{id: 'req_in', label: 'Required', type: 'string', required: true}];
        sampleFlow.nodes = [nodeP, nodeO]; // Only these two nodes
        sampleFlow.connections = []; // No connections

        const validation = service.validateFlow(sampleFlow);
        expect(validation.valid).toBe(false);
        expect(validation.errors.some(e => e.includes(`Node "${nodeP.data.title}" missing required inputs: Required`))).toBe(true);
      });
    });
  });
});
