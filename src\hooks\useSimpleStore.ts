import { useState } from 'react';

// Simple store without database for testing
export const useSimpleStore = () => {
  const [initialized, setInitialized] = useState(true);

  // Mock data for testing
  const mockAgents = [
    {
      id: '1',
      name: 'Code Reviewer',
      role: 'Senior Developer',
      systemPrompt: 'You are a senior developer conducting code reviews.',
      personality: 'Thorough, constructive, detail-oriented',
      enabled: true,
      expertise: ['React', 'TypeScript', 'Testing'],
      avatar: '👨‍💻'
    }
  ];

  const mockPrompts = [
    {
      id: '1',
      name: 'Test Prompt',
      prompt: 'This is a test prompt',
      variables: {},
      tags: ['test'],
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  const mockResults = [
    {
      id: '1',
      type: 'prompt_test',
      timestamp: new Date(),
      prompt: 'Test prompt',
      response: 'Test response',
      score: { fidelity: 85, adherence: 90, consistency: 88, creativity: 75 }
    }
  ];

  return {
    initialized,
    agents: mockAgents,
    enabledAgents: mockAgents.filter(a => a.enabled),
    prompts: mockPrompts,
    results: mockResults,
    
    // Mock functions
    addAgent: async () => mockAgents[0],
    updateAgent: async () => {},
    deleteAgent: async () => {},
    toggleAgent: () => {},
    
    addPrompt: async () => mockPrompts[0],
    updatePrompt: async () => {},
    deletePrompt: async () => {},
    
    addResult: async () => {},
    clearResults: async () => {},
    
    getCompatibleAgents: () => mockAgents,
    runIntegratedTest: async () => mockResults[0],
    
    // Database functions (mocked)
    createBackup: async () => 'backup_123',
    restoreBackup: async () => {},
    getBackups: async () => [],
    deleteBackup: async () => {},
    exportData: async () => ({}),
    importData: async () => {},
    getStats: async () => ({ prompts: 1, agents: 1, results: 1, backups: 0, totalSize: 0 }),
    cleanup: async () => {}
  };
};
