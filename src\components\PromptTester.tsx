
import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Slider } from '@/components/ui/slider';
import { Play, Settings, Loader2, Plus, Trash2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';
import { promptTestingService } from '@/lib/prompt-testing';

interface PromptTesterProps {
  onResults: (results: any) => void;
}

export const PromptTester = ({ onResults }: PromptTesterProps) => {
  const [prompt, setPrompt] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModels, setSelectedModels] = useState<Record<string, boolean>>({});
  const [temperature, setTemperature] = useState(0.7);
  const [maxTokens, setMaxTokens] = useState(1024);
  const [variables, setVariables] = useState<Array<{ key: string; value: string }>>([]);
  const [availableModels, setAvailableModels] = useState<Array<{ id: string; name: string; provider: string }>>([]);

  useEffect(() => {
    // Load available models from the service with error handling
    try {
      const models = promptTestingService.getAvailableModels();
      setAvailableModels(models);

      // Set default selections (OpenAI and Anthropic models)
      const defaultSelections: Record<string, boolean> = {};
      models.forEach(model => {
        defaultSelections[model.id] = model.provider === 'OpenAI' || model.provider === 'Anthropic';
      });
      setSelectedModels(defaultSelections);
    } catch (error) {
      console.error('Failed to load available models:', error);
      // Fallback to basic models
      const fallbackModels = [
        { id: 'openai:gpt-4o', name: 'GPT-4o', provider: 'OpenAI' },
        { id: 'anthropic:claude-3-5-sonnet-latest', name: 'Claude 3.5 Sonnet', provider: 'Anthropic' },
        { id: 'google:gemini-2.5-flash', name: 'Gemini 2.5 Flash', provider: 'Google' }
      ];
      setAvailableModels(fallbackModels);
      setSelectedModels({
        'openai:gpt-4': true,
        'anthropic:claude-3-sonnet': true
      });
    }
  }, []);

  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  const handleAddVariable = () => {
    setVariables(prev => [...prev, { key: '', value: '' }]);
  };

  const handleRemoveVariable = (index: number) => {
    setVariables(prev => prev.filter((_, i) => i !== index));
  };

  const handleVariableChange = (index: number, field: 'key' | 'value', value: string) => {
    setVariables(prev => prev.map((variable, i) =>
      i === index ? { ...variable, [field]: value } : variable
    ));
  };

  const handleRunTest = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt to test",
        variant: "destructive"
      });
      return;
    }

    const activeModels = Object.entries(selectedModels)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);

    if (activeModels.length === 0) {
      toast({
        title: "Error",
        description: "Please select at least one model",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Convert variables array to object
      const variablesObject = variables.reduce((acc, { key, value }) => {
        if (key.trim()) {
          acc[key.trim()] = value;
        }
        return acc;
      }, {} as Record<string, string>);

      const testResult = await promptTestingService.runPromptTest({
        prompt,
        systemPrompt: systemPrompt || undefined,
        models: activeModels,
        temperature,
        maxTokens,
        variables: Object.keys(variablesObject).length > 0 ? variablesObject : undefined
      });

      // Convert test result to the format expected by the store
      const storeResult = {
        id: testResult.id,
        timestamp: testResult.timestamp,
        prompt: testResult.prompt,
        systemPrompt: testResult.systemPrompt,
        variables: testResult.variables,
        results: testResult.results.map(result => ({
          model: result.model,
          provider: result.provider,
          response: result.response,
          timeToCompletion: result.timeToCompletion,
          tokens: result.tokens.total,
          score: result.score,
          error: result.error
        })),
        summary: testResult.summary,
        metadata: testResult.metadata
      };

      onResults(storeResult);

      toast({
        title: "Test Complete",
        description: `Successfully tested across ${testResult.metadata.successfulTests}/${testResult.metadata.totalModels} models`,
      });
    } catch (error) {
      console.error('Prompt test failed:', error);
      toast({
        title: "Test Failed",
        description: error instanceof Error ? error.message : "An unexpected error occurred",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Prompt Input */}
        <div className="lg:col-span-2 space-y-4">
          <Tabs defaultValue="prompt" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="prompt">Prompt</TabsTrigger>
              <TabsTrigger value="variables">Variables</TabsTrigger>
              <TabsTrigger value="settings">Settings</TabsTrigger>
            </TabsList>

            <TabsContent value="prompt" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="system-prompt" className="text-slate-200">System Prompt (Optional)</Label>
                    <Textarea
                      id="system-prompt"
                      placeholder="Enter system instructions here..."
                      value={systemPrompt}
                      onChange={(e) => setSystemPrompt(e.target.value)}
                      className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[100px]"
                    />
                  </div>

                  <div>
                    <Label htmlFor="user-prompt" className="text-slate-200">User Prompt *</Label>
                    <Textarea
                      id="user-prompt"
                      placeholder="Enter your prompt to test across multiple models... Use {{variable}} for dynamic content."
                      value={prompt}
                      onChange={(e) => setPrompt(e.target.value)}
                      className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[200px]"
                    />
                  </div>
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="variables" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-semibold text-slate-200">Variables</h3>
                  <Button onClick={handleAddVariable} size="sm" variant="outline">
                    <Plus className="w-4 h-4 mr-2" />
                    Add Variable
                  </Button>
                </div>

                <div className="space-y-3">
                  {variables.map((variable, index) => (
                    <div key={index} className="flex gap-2">
                      <Input
                        placeholder="Variable name"
                        value={variable.key}
                        onChange={(e) => handleVariableChange(index, 'key', e.target.value)}
                        className="bg-slate-900/50 border-slate-600 text-white"
                      />
                      <Input
                        placeholder="Value"
                        value={variable.value}
                        onChange={(e) => handleVariableChange(index, 'value', e.target.value)}
                        className="bg-slate-900/50 border-slate-600 text-white"
                      />
                      <Button
                        onClick={() => handleRemoveVariable(index)}
                        size="sm"
                        variant="ghost"
                        className="text-red-400 hover:text-red-300"
                      >
                        <Trash2 className="w-4 h-4" />
                      </Button>
                    </div>
                  ))}

                  {variables.length === 0 && (
                    <p className="text-slate-400 text-sm">
                      No variables defined. Use {"{{variableName}}"} in your prompt and define values here.
                    </p>
                  )}
                </div>
              </Card>
            </TabsContent>

            <TabsContent value="settings" className="space-y-4">
              <Card className="bg-slate-800/50 border-slate-700 p-6">
                <div className="space-y-6">
                  <div>
                    <Label className="text-slate-200">Temperature: {temperature}</Label>
                    <Slider
                      value={[temperature]}
                      onValueChange={([value]) => setTemperature(value)}
                      max={2}
                      min={0}
                      step={0.1}
                      className="mt-2"
                    />
                    <p className="text-xs text-slate-400 mt-1">
                      Controls randomness. Lower values make responses more focused.
                    </p>
                  </div>

                  <div>
                    <Label className="text-slate-200">Max Tokens: {maxTokens}</Label>
                    <Slider
                      value={[maxTokens]}
                      onValueChange={([value]) => setMaxTokens(value)}
                      max={4096}
                      min={100}
                      step={100}
                      className="mt-2"
                    />
                    <p className="text-xs text-slate-400 mt-1">
                      Maximum number of tokens in the response.
                    </p>
                  </div>
                </div>
              </Card>
            </TabsContent>
          </Tabs>
        </div>

        {/* Model Selection */}
        <div className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold mb-4 text-slate-200">Select Models</h3>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {availableModels.map((model) => (
                <div key={model.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={model.id}
                    checked={selectedModels[model.id] || false}
                    onCheckedChange={() => handleModelToggle(model.id)}
                    className="border-slate-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor={model.id} className="text-slate-200 cursor-pointer">
                      {model.name}
                    </Label>
                    <p className="text-xs text-slate-400">{model.provider}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Button
            onClick={handleRunTest}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Running Test...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Run Prompt Test
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
