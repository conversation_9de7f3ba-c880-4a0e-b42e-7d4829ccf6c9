
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Play, Settings, Loader2 } from 'lucide-react';
import { toast } from '@/hooks/use-toast';

interface PromptTesterProps {
  onResults: (results: any) => void;
}

export const PromptTester = ({ onResults }: PromptTesterProps) => {
  const [prompt, setPrompt] = useState('');
  const [systemPrompt, setSystemPrompt] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [selectedModels, setSelectedModels] = useState({
    openai: true,
    anthropic: true,
    gemini: false,
    mistral: false,
  });

  const models = [
    { id: 'openai', name: 'OpenAI GPT-4', provider: 'OpenAI' },
    { id: 'anthropic', name: 'Claude 3.5 Sonnet', provider: 'Anthropic' },
    { id: 'gemini', name: 'Gemini Pro', provider: 'Google' },
    { id: 'mistral', name: 'Mistral Large', provider: 'Mistral' },
  ];

  const handleModelToggle = (modelId: string) => {
    setSelectedModels(prev => ({
      ...prev,
      [modelId]: !prev[modelId]
    }));
  };

  const handleRunTest = async () => {
    if (!prompt.trim()) {
      toast({
        title: "Error",
        description: "Please enter a prompt to test",
        variant: "destructive"
      });
      return;
    }

    const activeModels = Object.entries(selectedModels)
      .filter(([_, selected]) => selected)
      .map(([id]) => id);

    if (activeModels.length === 0) {
      toast({
        title: "Error", 
        description: "Please select at least one model",
        variant: "destructive"
      });
      return;
    }

    setIsLoading(true);

    try {
      // Simulate API calls to different models
      const mockResults = {
        id: Date.now(),
        timestamp: new Date(),
        prompt,
        systemPrompt,
        results: activeModels.map(modelId => {
          const model = models.find(m => m.id === modelId);
          return {
            model: model?.name || modelId,
            provider: model?.provider || 'Unknown',
            response: `Mock response from ${model?.name} for: "${prompt.slice(0, 50)}..."`,
            timeToCompletion: Math.random() * 3000 + 500,
            tokens: Math.floor(Math.random() * 500) + 100,
            score: {
              fidelity: Math.random() * 100,
              adherence: Math.random() * 100,
              consistency: Math.random() * 100,
            }
          };
        })
      };

      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 2000));

      onResults(mockResults);
      toast({
        title: "Test Complete",
        description: `Successfully tested across ${activeModels.length} models`,
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to run prompt test",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Prompt Input */}
        <div className="lg:col-span-2 space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <div className="space-y-4">
              <div>
                <Label htmlFor="system-prompt" className="text-slate-200">System Prompt (Optional)</Label>
                <Textarea
                  id="system-prompt"
                  placeholder="Enter system instructions here..."
                  value={systemPrompt}
                  onChange={(e) => setSystemPrompt(e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[100px]"
                />
              </div>
              
              <div>
                <Label htmlFor="user-prompt" className="text-slate-200">User Prompt *</Label>
                <Textarea
                  id="user-prompt"
                  placeholder="Enter your prompt to test across multiple models..."
                  value={prompt}
                  onChange={(e) => setPrompt(e.target.value)}
                  className="mt-2 bg-slate-900/50 border-slate-600 text-white placeholder-slate-400 min-h-[200px]"
                />
              </div>
            </div>
          </Card>
        </div>

        {/* Model Selection */}
        <div className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700 p-6">
            <h3 className="text-lg font-semibold mb-4 text-slate-200">Select Models</h3>
            <div className="space-y-3">
              {models.map((model) => (
                <div key={model.id} className="flex items-center space-x-3">
                  <Checkbox
                    id={model.id}
                    checked={selectedModels[model.id as keyof typeof selectedModels]}
                    onCheckedChange={() => handleModelToggle(model.id)}
                    className="border-slate-500"
                  />
                  <div className="flex-1">
                    <Label htmlFor={model.id} className="text-slate-200 cursor-pointer">
                      {model.name}
                    </Label>
                    <p className="text-xs text-slate-400">{model.provider}</p>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          <Button 
            onClick={handleRunTest}
            disabled={isLoading}
            className="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            size="lg"
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Running Test...
              </>
            ) : (
              <>
                <Play className="w-4 h-4 mr-2" />
                Run Prompt Test
              </>
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};
