
import { useEffect, useState } from 'react';
import { promptStore, Agent, PromptVariant, TestResult } from '@/store/promptStore';

export const usePromptStore = () => {
  const [, forceUpdate] = useState({});
  const [initialized, setInitialized] = useState(false);

  useEffect(() => {
    const initializeStore = async () => {
      try {
        // Set a timeout for initialization to prevent hanging
        const initPromise = promptStore.initialize();
        const timeoutPromise = new Promise((_, reject) =>
          setTimeout(() => reject(new Error('Initialization timeout')), 5000)
        );

        await Promise.race([initPromise, timeoutPromise]);
        setInitialized(true);
      } catch (error) {
        console.warn('Store initialization failed, using memory storage:', error);
        setInitialized(true); // Continue with memory storage
      }
    };

    initializeStore();

    const unsubscribe = promptStore.subscribe(() => {
      forceUpdate({});
    });
    return unsubscribe;
  }, []);

  return {
    // Initialization status
    initialized,

    // Agent methods
    agents: promptStore.getAgents(),
    enabledAgents: promptStore.getEnabledAgents(),
    addAgent: promptStore.addAgent.bind(promptStore),
    updateAgent: promptStore.updateAgent.bind(promptStore),
    deleteAgent: promptStore.deleteAgent.bind(promptStore),
    toggleAgent: promptStore.toggleAgent.bind(promptStore),

    // Prompt methods
    prompts: promptStore.getPrompts(),
    addPrompt: promptStore.addPrompt.bind(promptStore),
    updatePrompt: promptStore.updatePrompt.bind(promptStore),
    deletePrompt: promptStore.deletePrompt.bind(promptStore),

    // Results methods
    results: promptStore.getResults(),
    addResult: promptStore.addResult.bind(promptStore),

    // Integration methods
    getCompatibleAgents: promptStore.getCompatibleAgents.bind(promptStore),
    runIntegratedTest: promptStore.runIntegratedTest.bind(promptStore),

    // Backup and restore
    createBackup: promptStore.createBackup.bind(promptStore),
    restoreBackup: promptStore.restoreBackup.bind(promptStore),
    getBackups: promptStore.getBackups.bind(promptStore),
    deleteBackup: promptStore.deleteBackup.bind(promptStore),

    // Export and import
    exportData: promptStore.exportData.bind(promptStore),
    importData: promptStore.importData.bind(promptStore),

    // Database utilities
    getStats: promptStore.getStats.bind(promptStore),
    cleanup: promptStore.cleanup.bind(promptStore)
  };
};
