
import { useEffect, useState } from 'react';
import { promptStore, Agent, PromptVariant, TestResult } from '@/store/promptStore';

export const usePromptStore = () => {
  const [, forceUpdate] = useState({});

  useEffect(() => {
    const unsubscribe = promptStore.subscribe(() => {
      forceUpdate({});
    });
    return unsubscribe;
  }, []);

  return {
    // Agent methods
    agents: promptStore.getAgents(),
    enabledAgents: promptStore.getEnabledAgents(),
    addAgent: promptStore.addAgent.bind(promptStore),
    updateAgent: promptStore.updateAgent.bind(promptStore),
    deleteAgent: promptStore.deleteAgent.bind(promptStore),
    toggleAgent: promptStore.toggleAgent.bind(promptStore),

    // Prompt methods
    prompts: promptStore.getPrompts(),
    addPrompt: promptStore.addPrompt.bind(promptStore),
    updatePrompt: promptStore.updatePrompt.bind(promptStore),
    deletePrompt: promptStore.deletePrompt.bind(promptStore),

    // Results methods
    results: promptStore.getResults(),
    addResult: promptStore.addResult.bind(promptStore),

    // Integration methods
    getCompatibleAgents: promptStore.getCompatibleAgents.bind(promptStore),
    runIntegratedTest: promptStore.runIntegratedTest.bind(promptStore)
  };
};
