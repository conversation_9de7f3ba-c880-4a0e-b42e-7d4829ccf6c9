# 🔧 Troubleshooting Guide

This guide helps resolve common issues when setting up and running Prompt Studio.

## 🚨 Common Issues & Solutions

### 1. Browserslist Database Error

**Error:** `'bun' is not recognized as an internal or external command`

**Cause:** The browserslist update tool is trying to use `bun` instead of `npm`.

**Solutions:**

#### Option A: Use the Fix Script (Recommended)
```bash
# Run the automated fix
./fix-issues.cmd
```

#### Option B: Manual Fix
```bash
# Update browserslist manually
npx browserslist@latest --update-db

# If that fails, try:
npm update caniuse-lite browserslist
```

#### Option C: Use Package Script
```bash
npm run update-browserslist
```

### 2. NPM Vulnerabilities

**Error:** `5 vulnerabilities (1 low, 4 moderate)`

**Solutions:**

#### Option A: Automated Fix
```bash
npm audit fix
```

#### Option B: Force Fix (if automated fails)
```bash
npm audit fix --force
```

#### Option C: Use Package Script
```bash
npm run fix-vulnerabilities
```

**Note:** Some vulnerabilities may be in development dependencies and don't affect production builds.

### 3. Dependencies Installation Issues

**Error:** `npm install` fails or hangs

**Solutions:**

#### Clear Cache and Reinstall
```bash
# Clear npm cache
npm cache clean --force

# Delete node_modules and package-lock.json
rmdir /s node_modules
del package-lock.json

# Reinstall
npm install
```

#### Use Different Registry
```bash
# Use npm registry
npm install --registry https://registry.npmjs.org/

# Or use yarn instead
yarn install
```

### 4. Development Server Won't Start

**Error:** `npm run dev` fails or hangs

**Solutions:**

#### Check Port Availability
```bash
# Kill processes on port 5173
netstat -ano | findstr :5173
taskkill /PID <PID_NUMBER> /F

# Or use different port
npm run dev -- --port 3000
```

#### Clear Vite Cache
```bash
# Clear Vite cache
rmdir /s node_modules\.vite
npm run dev
```

### 5. TypeScript Errors

**Error:** TypeScript compilation errors

**Solutions:**

#### Check TypeScript Configuration
```bash
# Run type checking
npx tsc --noEmit

# Fix linting issues
npm run lint
```

#### Update TypeScript
```bash
npm update typescript @types/node @types/react @types/react-dom
```

### 6. Environment Variables Not Working

**Error:** API calls fail with authentication errors

**Solutions:**

#### Check .env File
1. Ensure `.env` file exists in project root
2. Verify API keys are correctly formatted:
   ```env
   VITE_OPENAI_API_KEY=sk-your-actual-key-here
   VITE_ANTHROPIC_API_KEY=sk-ant-your-actual-key-here
   ```
3. No spaces around the `=` sign
4. No quotes around the values
5. Restart development server after changes

#### Verify API Keys
- OpenAI keys start with `sk-`
- Anthropic keys start with `sk-ant-`
- Test keys in the respective platforms

### 7. Build Failures

**Error:** `npm run build` fails

**Solutions:**

#### Check for Errors
```bash
# Run with verbose output
npm run build -- --verbose

# Check for TypeScript errors
npx tsc --noEmit
```

#### Clear Build Cache
```bash
# Clear dist folder
rmdir /s dist

# Clear Vite cache
rmdir /s node_modules\.vite

# Rebuild
npm run build
```

## 🛠️ Automated Fixes

### Use the Launcher Menu
1. Run `start-prompt-studio.cmd`
2. Choose option 5: "Fix common issues"
3. Let the script resolve problems automatically

### Use the Fix Script
```bash
# Run the dedicated fix script
./fix-issues.cmd
```

### Use Package Scripts
```bash
# Fix vulnerabilities
npm run fix-vulnerabilities

# Update browserslist
npm run update-browserslist

# Complete setup
npm run setup
```

## 🔍 Diagnostic Commands

### Check System Requirements
```bash
# Check Node.js version (should be 18+)
node --version

# Check npm version
npm --version

# Check if all dependencies are installed
npm list --depth=0
```

### Check Project Health
```bash
# Run audit
npm audit

# Check for outdated packages
npm outdated

# Verify TypeScript compilation
npx tsc --noEmit
```

### Check Environment
```bash
# List environment variables (Windows)
set | findstr VITE_

# Check if .env file exists
dir .env
```

## 🆘 Getting Help

If you're still experiencing issues:

1. **Check the Console**: Look for error messages in browser developer tools
2. **Check Terminal Output**: Review the full error messages in your terminal
3. **Try Safe Mode**: Run with minimal configuration
4. **Update Everything**: Update Node.js, npm, and all dependencies
5. **Fresh Install**: Delete `node_modules` and reinstall

### Safe Mode Setup
```bash
# Minimal installation
npm install --production
npm run dev
```

### Fresh Install
```bash
# Complete reset
rmdir /s node_modules
del package-lock.json
npm cache clean --force
npm install
```

## 📞 Support Resources

- **Documentation**: Check `README.md` and `USAGE.md`
- **Package Issues**: Check npm package pages for known issues
- **Node.js Issues**: Visit [nodejs.org](https://nodejs.org/) for Node.js problems
- **Vite Issues**: Check [vitejs.dev](https://vitejs.dev/) for build tool problems

---

**Most issues can be resolved with the automated fix scripts! 🚀**
