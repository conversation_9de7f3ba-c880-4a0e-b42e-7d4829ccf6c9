import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { aiService, AI_PROVIDERS } from '@/lib/ai-service';
import { Settings, Key, Zap, AlertCircle, CheckCircle } from 'lucide-react';

interface AISettings {
  provider: string;
  model: string;
  temperature: number;
  maxTokens: number;
  openaiApiKey: string;
  anthropicApiKey: string;
}

export const AIConfiguration = () => {
  const [settings, setSettings] = useState<AISettings>({
    provider: 'openai',
    model: 'gpt-4',
    temperature: 0.7,
    maxTokens: 2048,
    openaiApiKey: '',
    anthropicApiKey: ''
  });

  const [testResult, setTestResult] = useState<{ success: boolean; message: string } | null>(null);
  const [isTestingConnection, setIsTestingConnection] = useState(false);

  useEffect(() => {
    // Load settings from localStorage
    const savedSettings = localStorage.getItem('ai-settings');
    if (savedSettings) {
      try {
        const parsed = JSON.parse(savedSettings);
        setSettings(prev => ({ ...prev, ...parsed }));
      } catch (error) {
        console.error('Failed to parse saved AI settings:', error);
      }
    }

    // Load default settings from service
    const defaults = aiService.getDefaultSettings();
    setSettings(prev => ({
      ...prev,
      provider: defaults.provider,
      model: defaults.model,
      temperature: defaults.temperature,
      maxTokens: defaults.maxTokens
    }));
  }, []);

  const saveSettings = () => {
    localStorage.setItem('ai-settings', JSON.stringify(settings));

    // Update environment variables (for development)
    if (typeof window !== 'undefined') {
      (window as any).__AI_CONFIG__ = {
        VITE_OPENAI_API_KEY: settings.openaiApiKey,
        VITE_ANTHROPIC_API_KEY: settings.anthropicApiKey,
        VITE_DEFAULT_AI_PROVIDER: settings.provider,
        VITE_DEFAULT_MODEL: settings.model,
        VITE_DEFAULT_TEMPERATURE: settings.temperature.toString(),
        VITE_DEFAULT_MAX_TOKENS: settings.maxTokens.toString()
      };
    }

    // Refresh the AI service configuration
    aiService.refreshConfiguration();
  };

  const testConnection = async () => {
    setIsTestingConnection(true);
    setTestResult(null);

    try {
      const response = await aiService.generateResponse({
        provider: settings.provider,
        model: settings.model,
        messages: [
          { role: 'user', content: 'Hello! Please respond with "Connection test successful."' }
        ],
        temperature: 0.1,
        maxTokens: 50
      });

      if (response.content.toLowerCase().includes('connection test successful')) {
        setTestResult({ success: true, message: 'Connection test successful!' });
      } else {
        setTestResult({ success: true, message: 'Connection established, but unexpected response.' });
      }
    } catch (error) {
      setTestResult({ 
        success: false, 
        message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}` 
      });
    } finally {
      setIsTestingConnection(false);
    }
  };

  const getProviderStatus = (provider: string) => {
    const hasKey = provider === 'openai' ? !!settings.openaiApiKey : !!settings.anthropicApiKey;
    return hasKey ? 'configured' : 'not-configured';
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center gap-2 mb-6">
        <Settings className="h-6 w-6" />
        <h2 className="text-2xl font-bold">AI Configuration</h2>
      </div>

      <Tabs defaultValue="providers" className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="providers">Providers</TabsTrigger>
          <TabsTrigger value="models">Models</TabsTrigger>
          <TabsTrigger value="parameters">Parameters</TabsTrigger>
        </TabsList>

        <TabsContent value="providers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5" />
                API Keys
              </CardTitle>
              <CardDescription>
                Configure your AI provider API keys. Keys are stored locally in your browser.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="openai-key">OpenAI API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="openai-key"
                    type="password"
                    placeholder="sk-..."
                    value={settings.openaiApiKey}
                    onChange={(e) => setSettings(prev => ({ ...prev, openaiApiKey: e.target.value }))}
                  />
                  <Badge variant={getProviderStatus('openai') === 'configured' ? 'default' : 'secondary'}>
                    {getProviderStatus('openai') === 'configured' ? 'Configured' : 'Not Set'}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="anthropic-key">Anthropic API Key</Label>
                <div className="flex gap-2">
                  <Input
                    id="anthropic-key"
                    type="password"
                    placeholder="sk-ant-..."
                    value={settings.anthropicApiKey}
                    onChange={(e) => setSettings(prev => ({ ...prev, anthropicApiKey: e.target.value }))}
                  />
                  <Badge variant={getProviderStatus('anthropic') === 'configured' ? 'default' : 'secondary'}>
                    {getProviderStatus('anthropic') === 'configured' ? 'Configured' : 'Not Set'}
                  </Badge>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="models" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Model Selection</CardTitle>
              <CardDescription>
                Choose your preferred AI provider and model.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="provider">AI Provider</Label>
                <Select value={settings.provider} onValueChange={(value) => {
                  setSettings(prev => ({ 
                    ...prev, 
                    provider: value,
                    model: AI_PROVIDERS[value]?.models[0] || prev.model
                  }));
                }}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {Object.entries(AI_PROVIDERS).map(([key, provider]) => (
                      <SelectItem key={key} value={key}>
                        {provider.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="model">Model</Label>
                <Select value={settings.model} onValueChange={(value) => {
                  setSettings(prev => ({ ...prev, model: value }));
                }}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {AI_PROVIDERS[settings.provider]?.models.map((model) => (
                      <SelectItem key={model} value={model}>
                        {model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="parameters" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Generation Parameters</CardTitle>
              <CardDescription>
                Fine-tune the AI response generation settings.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-2">
                <Label>Temperature: {settings.temperature}</Label>
                <Slider
                  value={[settings.temperature]}
                  onValueChange={([value]) => setSettings(prev => ({ ...prev, temperature: value }))}
                  max={2}
                  min={0}
                  step={0.1}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  Controls randomness. Lower values make responses more focused and deterministic.
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="max-tokens">Max Tokens: {settings.maxTokens}</Label>
                <Slider
                  value={[settings.maxTokens]}
                  onValueChange={([value]) => setSettings(prev => ({ ...prev, maxTokens: value }))}
                  max={4096}
                  min={100}
                  step={100}
                  className="w-full"
                />
                <p className="text-sm text-muted-foreground">
                  Maximum number of tokens in the response.
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Connection Test
          </CardTitle>
          <CardDescription>
            Test your AI provider connection and settings.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button 
              onClick={testConnection} 
              disabled={isTestingConnection || !getProviderStatus(settings.provider)}
              className="flex items-center gap-2"
            >
              {isTestingConnection ? 'Testing...' : 'Test Connection'}
            </Button>
            <Button onClick={saveSettings} variant="outline">
              Save Settings
            </Button>
          </div>

          {testResult && (
            <Alert className={testResult.success ? 'border-green-500' : 'border-red-500'}>
              {testResult.success ? (
                <CheckCircle className="h-4 w-4 text-green-500" />
              ) : (
                <AlertCircle className="h-4 w-4 text-red-500" />
              )}
              <AlertDescription>{testResult.message}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
