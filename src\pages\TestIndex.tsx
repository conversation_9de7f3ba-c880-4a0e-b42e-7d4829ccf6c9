import { useState } from 'react';

const TestIndex = () => {
  const [test, setTest] = useState('Hello World');

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-400 via-purple-400 to-pink-400 bg-clip-text text-transparent mb-4">
          💠 Prompt Studio - Test Page
        </h1>
        <p className="text-slate-300 mb-8">
          This is a test page to verify the application is working.
        </p>
        
        <div className="bg-slate-800/50 p-6 rounded-lg border border-slate-700">
          <h2 className="text-xl font-semibold text-slate-200 mb-4">Test Status</h2>
          <div className="space-y-2">
            <p className="text-green-400">✅ React is working</p>
            <p className="text-green-400">✅ TypeScript is working</p>
            <p className="text-green-400">✅ Tailwind CSS is working</p>
            <p className="text-green-400">✅ State management is working: {test}</p>
          </div>
          
          <button 
            onClick={() => setTest('State Updated!')}
            className="mt-4 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            Test State Update
          </button>
        </div>
        
        <div className="mt-8 bg-slate-800/50 p-6 rounded-lg border border-slate-700">
          <h2 className="text-xl font-semibold text-slate-200 mb-4">Next Steps</h2>
          <p className="text-slate-300">
            If you can see this page, the basic React app is working. 
            The issue might be with specific components or dependencies in the main Index page.
          </p>
        </div>
      </div>
    </div>
  );
};

export default TestIndex;
